"""
Reconnection handler with multiple strategies and exponential backoff.

This module provides intelligent reconnection logic with different strategies
for handling WebSocket connection failures and network issues.
"""

import asyncio
import random
import time
from typing import Optional
from enum import Enum
from dataclasses import dataclass

from ..utils.logger import get_logger


class ReconnectionStrategy(Enum):
    """Reconnection strategies."""
    FIXED = "fixed"           # Fixed delay between attempts
    LINEAR = "linear"         # Linear increase in delay
    EXPONENTIAL = "exponential"  # Exponential backoff
    JITTERED = "jittered"     # Exponential with random jitter


@dataclass
class ReconnectionConfig:
    """Configuration for reconnection behavior."""
    initial_delay: float = 1.0      # Initial delay in seconds
    max_delay: float = 300.0        # Maximum delay in seconds
    max_attempts: Optional[int] = None  # Maximum attempts (None = unlimited)
    backoff_factor: float = 2.0     # Backoff multiplication factor
    jitter_range: float = 0.1       # Jitter range (0.0-1.0)
    reset_threshold: float = 300.0  # Successful connection time to reset counter


class ReconnectionHandler:
    """
    Handles reconnection logic with configurable strategies.
    
    This class implements various reconnection strategies including
    exponential backoff, jitter, and attempt limiting.
    """
    
    def __init__(self, 
                 strategy: ReconnectionStrategy = ReconnectionStrategy.EXPONENTIAL,
                 config: Optional[ReconnectionConfig] = None):
        """
        Initialize reconnection handler.
        
        Args:
            strategy: Reconnection strategy to use
            config: Reconnection configuration
        """
        self.strategy = strategy
        self.config = config or ReconnectionConfig()
        self.logger = get_logger(self.__class__.__name__)
        
        # State tracking
        self.attempt_count = 0
        self.last_connection_time: Optional[float] = None
        self.last_attempt_time: Optional[float] = None
        self.total_attempts = 0
        
        # Statistics
        self.successful_reconnections = 0
        self.failed_reconnections = 0
        self.total_backoff_time = 0.0
    
    async def get_next_delay(self) -> float:
        """
        Calculate next reconnection delay based on strategy.
        
        Returns:
            Delay in seconds before next reconnection attempt
        """
        self.attempt_count += 1
        self.total_attempts += 1
        current_time = time.time()
        
        # Check if max attempts exceeded
        if (self.config.max_attempts is not None and 
            self.attempt_count > self.config.max_attempts):
            raise MaxReconnectionAttemptsExceeded(
                f"Maximum reconnection attempts ({self.config.max_attempts}) exceeded"
            )
        
        # Calculate base delay based on strategy
        if self.strategy == ReconnectionStrategy.FIXED:
            delay = self.config.initial_delay
            
        elif self.strategy == ReconnectionStrategy.LINEAR:
            delay = self.config.initial_delay * self.attempt_count
            
        elif self.strategy == ReconnectionStrategy.EXPONENTIAL:
            delay = self.config.initial_delay * (self.config.backoff_factor ** (self.attempt_count - 1))
            
        elif self.strategy == ReconnectionStrategy.JITTERED:
            base_delay = self.config.initial_delay * (self.config.backoff_factor ** (self.attempt_count - 1))
            jitter = base_delay * self.config.jitter_range * (random.random() * 2 - 1)
            delay = base_delay + jitter
        
        else:
            delay = self.config.initial_delay
        
        # Apply maximum delay limit
        delay = min(delay, self.config.max_delay)
        
        # Ensure minimum delay
        delay = max(delay, 0.1)
        
        self.last_attempt_time = current_time
        self.total_backoff_time += delay
        
        self.logger.debug(f"Calculated reconnection delay: {delay}s "
                         f"(attempt {self.attempt_count}, strategy: {self.strategy.value})")
        
        return delay
    
    def on_connection_success(self):
        """
        Called when connection is successful.
        
        Resets attempt counter if connection was stable long enough.
        """
        current_time = time.time()
        self.last_connection_time = current_time
        
        # Check if we should reset the attempt counter
        if (self.last_attempt_time and 
            current_time - self.last_attempt_time >= self.config.reset_threshold):
            self.logger.info(f"Connection stable for {self.config.reset_threshold}s, "
                           f"resetting attempt counter")
            self.reset()
        
        self.successful_reconnections += 1
        self.logger.debug(f"Connection successful (attempt {self.attempt_count})")
    
    def on_connection_failure(self, error: Exception):
        """
        Called when connection fails.
        
        Args:
            error: Connection error
        """
        self.failed_reconnections += 1
        self.logger.debug(f"Connection failed (attempt {self.attempt_count}): {error}")
    
    def reset(self):
        """Reset reconnection state."""
        self.attempt_count = 0
        self.last_attempt_time = None
        self.logger.debug("Reconnection handler reset")
    
    def should_attempt_reconnection(self) -> bool:
        """
        Check if reconnection should be attempted.
        
        Returns:
            True if reconnection should be attempted, False otherwise
        """
        if self.config.max_attempts is None:
            return True
        
        return self.attempt_count < self.config.max_attempts
    
    def get_statistics(self) -> dict:
        """
        Get reconnection statistics.
        
        Returns:
            Dictionary with reconnection statistics
        """
        return {
            'strategy': self.strategy.value,
            'attempt_count': self.attempt_count,
            'total_attempts': self.total_attempts,
            'successful_reconnections': self.successful_reconnections,
            'failed_reconnections': self.failed_reconnections,
            'total_backoff_time_seconds': round(self.total_backoff_time, 2),
            'last_connection_time': self.last_connection_time,
            'last_attempt_time': self.last_attempt_time,
            'config': {
                'initial_delay': self.config.initial_delay,
                'max_delay': self.config.max_delay,
                'max_attempts': self.config.max_attempts,
                'backoff_factor': self.config.backoff_factor,
                'jitter_range': self.config.jitter_range,
                'reset_threshold': self.config.reset_threshold
            }
        }


class MaxReconnectionAttemptsExceeded(Exception):
    """Exception raised when maximum reconnection attempts are exceeded."""
    pass
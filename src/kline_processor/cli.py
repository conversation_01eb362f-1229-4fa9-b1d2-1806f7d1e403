"""
Command-line interface for K-line processor.

This module provides CLI commands for interacting with the K-line processor
application including data fetching, streaming, and aggregation operations.
"""

import asyncio
import click
import json
from datetime import datetime, timezone
from typing import Optional, List

from .main import KlineProcessor
from .services.websocket_stream_service import StreamSubscription
from .config.settings import Settings
from .utils.logger import get_logger


def validate_symbol(ctx, param, value):
    """Validate trading symbol format."""
    if value and not value.isupper():
        raise click.BadParameter('Symbol must be uppercase (e.g., BTCUSDT)')
    return value


def validate_interval(ctx, param, value):
    """Validate timeframe interval format."""
    valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
    if value and value not in valid_intervals:
        raise click.BadParameter(f'Invalid interval. Must be one of: {", ".join(valid_intervals)}')
    return value


def validate_timestamp(ctx, param, value):
    """Validate timestamp parameter."""
    if value is None:
        return None
    
    try:
        # Try parsing as integer (milliseconds)
        return int(value)
    except ValueError:
        try:
            # Try parsing as ISO datetime string
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return int(dt.timestamp() * 1000)
        except ValueError:
            raise click.BadParameter('Invalid timestamp. Use milliseconds or ISO format (e.g., 2022-01-01T00:00:00Z)')


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--config-file', type=click.Path(exists=True), help='Configuration file path')
@click.pass_context
def cli(ctx, debug, config_file):
    """K-line processor CLI - Real-time cryptocurrency data processing."""
    ctx.ensure_object(dict)
    
    # Initialize settings
    settings = Settings()
    if debug:
        settings.debug = True
        settings.log_level = 'DEBUG'
    
    if config_file:
        # Load configuration from file if provided
        settings = Settings(_env_file=config_file)
    
    ctx.obj['settings'] = settings
    ctx.obj['logger'] = get_logger('cli')


@cli.command()
@click.option('--symbol', required=True, callback=validate_symbol,
              help='Trading symbol (e.g., BTCUSDT)')
@click.option('--interval', required=True, callback=validate_interval,
              help='K-line interval (e.g., 1m, 5m, 1h, 1d)')
@click.option('--start-time', callback=validate_timestamp,
              help='Start time (milliseconds or ISO format)')
@click.option('--end-time', callback=validate_timestamp,
              help='End time (milliseconds or ISO format)')
@click.option('--limit', type=int, default=1000,
              help='Maximum number of K-lines to fetch')
@click.option('--output', type=click.Path(), help='Output file path (JSON format)')
@click.pass_context
def fetch(ctx, symbol, interval, start_time, end_time, limit, output):
    """Fetch historical K-line data."""
    
    async def run_fetch():
        settings = ctx.obj['settings']
        logger = ctx.obj['logger']
        
        try:
            async with KlineProcessor(settings) as processor:
                logger.info(f"Fetching historical data for {symbol}:{interval}")
                
                result = await processor.fetch_historical_data(
                    symbol=symbol,
                    interval=interval,
                    start_time=start_time,
                    end_time=end_time
                )
                
                if output:
                    # Save to file
                    with open(output, 'w') as f:
                        json.dump(result, f, indent=2, default=str)
                    logger.info(f"Results saved to {output}")
                else:
                    # Print to stdout
                    click.echo(json.dumps(result, indent=2, default=str))
                
        except Exception as e:
            logger.error(f"Fetch operation failed: {e}")
            raise click.ClickException(str(e))
    
    asyncio.run(run_fetch())


@cli.command()
@click.option('--symbols', required=True,
              help='Comma-separated list of symbols (e.g., BTCUSDT,ETHUSDT)')
@click.option('--interval', required=True, callback=validate_interval,
              help='K-line interval (e.g., 1m, 5m, 1h)')
@click.option('--aggregate', multiple=True,
              help='Target intervals for aggregation (can be specified multiple times)')
@click.option('--output-stats', type=click.Path(),
              help='Output file for statistics (JSON format)')
@click.pass_context
def stream(ctx, symbols, interval, aggregate, output_stats):
    """Start real-time K-line streaming."""
    
    async def run_stream():
        settings = ctx.obj['settings']
        logger = ctx.obj['logger']
        
        try:
            # Parse symbols
            symbol_list = [s.strip().upper() for s in symbols.split(',')]
            
            # Create subscriptions
            subscriptions = []
            for symbol in symbol_list:
                subscription = StreamSubscription(
                    symbol=symbol,
                    interval=interval,
                    auto_aggregate=bool(aggregate),
                    target_intervals=list(aggregate) if aggregate else []
                )
                subscriptions.append(subscription)
            
            async with KlineProcessor(settings) as processor:
                logger.info(f"Starting stream for {len(symbol_list)} symbols: {', '.join(symbol_list)}")
                
                # Subscribe to streams
                success = await processor.subscribe_to_streams(subscriptions)
                if not success:
                    raise click.ClickException("Failed to subscribe to streams")
                
                logger.info("Streaming started. Press Ctrl+C to stop.")
                
                # Run until interrupted
                try:
                    await processor.run()
                except KeyboardInterrupt:
                    logger.info("Stream stopped by user")
                
                # Output statistics if requested
                if output_stats:
                    stats = processor.get_application_statistics()
                    with open(output_stats, 'w') as f:
                        json.dump(stats, f, indent=2, default=str)
                    logger.info(f"Statistics saved to {output_stats}")
                
        except Exception as e:
            logger.error(f"Stream operation failed: {e}")
            raise click.ClickException(str(e))
    
    asyncio.run(run_stream())




@cli.command()
@click.option('--output', type=click.Path(), help='Output file path (JSON format)')
@click.pass_context
def status(ctx, output):
    """Get application status and statistics."""
    
    async def run_status():
        settings = ctx.obj['settings']
        logger = ctx.obj['logger']
        
        try:
            async with KlineProcessor(settings) as processor:
                # Get statistics and health check
                stats = processor.get_application_statistics()
                health = await processor.health_check()
                
                result = {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'statistics': stats,
                    'health': health
                }
                
                if output:
                    # Save to file
                    with open(output, 'w') as f:
                        json.dump(result, f, indent=2, default=str)
                    logger.info(f"Status saved to {output}")
                else:
                    # Print to stdout
                    click.echo(json.dumps(result, indent=2, default=str))
                
        except Exception as e:
            logger.error(f"Status operation failed: {e}")
            raise click.ClickException(str(e))
    
    asyncio.run(run_status())


@cli.command()
@click.pass_context
def health(ctx):
    """Perform health check on all services."""
    
    async def run_health():
        settings = ctx.obj['settings']
        logger = ctx.obj['logger']
        
        try:
            async with KlineProcessor(settings) as processor:
                health_result = await processor.health_check()
                
                # Print health status
                if health_result['healthy']:
                    click.echo(click.style("✓ All services healthy", fg='green'))
                else:
                    click.echo(click.style("✗ Health check failed", fg='red'))
                    for issue in health_result['issues']:
                        click.echo(click.style(f"  - {issue}", fg='red'))
                
                # Print service details
                for service_name, service_health in health_result.get('services', {}).items():
                    status_color = 'green' if service_health.get('healthy', False) else 'red'
                    status_symbol = '✓' if service_health.get('healthy', False) else '✗'
                    click.echo(f"  {status_symbol} {service_name}: {click.style('healthy' if service_health.get('healthy', False) else 'unhealthy', fg=status_color)}")
                
                # Exit with error code if unhealthy
                if not health_result['healthy']:
                    raise click.ClickException("Health check failed")
                
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            raise click.ClickException(str(e))
    
    asyncio.run(run_health())


@cli.command()
@click.pass_context
def config(ctx):
    """Show current configuration."""
    settings = ctx.obj['settings']
    
    config_dict = {
        'environment': settings.environment,
        'debug': settings.debug,
        'log_level': settings.log_level,
        'redis': {
            'host': settings.redis_host,
            'port': settings.redis_port,
            'db': settings.redis_db
        },
        'exchange_api': {
            'base_url': settings.exchange_api_base_url,
            'rate_limit_requests': settings.rate_limit_requests,
            'rate_limit_period': settings.rate_limit_period
        },
        'websocket': {
            'base_url': settings.websocket_base_url,
            'auto_reconnect': settings.websocket_auto_reconnect,
            'max_reconnect_attempts': settings.websocket_max_reconnect_attempts
        },
        'services': {
            'historical_batch_size': settings.historical_batch_size,
            'aggregator_max_concurrent': settings.aggregator_max_concurrent,
            'stream_buffer_size': settings.stream_buffer_size
        }
    }
    
    click.echo(json.dumps(config_dict, indent=2))


if __name__ == '__main__':
    cli()
"""
Standalone script to run the FastAPI server without signal conflicts.
"""

import sys
import uvicorn
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from api.app import create_app
from kline_processor.config.settings import Settings


def main():
    """Run the FastAPI server."""
    settings = Settings()
    app = create_app(settings)
    
    # Run with uvicorn
    uvicorn.run(
        app,
        host=settings.api_host if hasattr(settings, 'api_host') else "0.0.0.0",
        port=settings.api_port if hasattr(settings, 'api_port') else 8088,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": settings.log_level,
                "handlers": ["default"],
            },
        }
    )


if __name__ == "__main__":
    main()
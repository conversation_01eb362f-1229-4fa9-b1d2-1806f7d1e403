"""
Redis client with async connection management and health monitoring.

This module provides a comprehensive Redis client implementation with
connection pooling, health checks, retry logic, and monitoring capabilities.
"""

import asyncio
import contextlib
from typing import Any, Dict, List, Optional, AsyncIterator, Union
from datetime import datetime, timedelta
import json

import redis.asyncio as redis
from redis.asyncio import ConnectionPool, Redis
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from ..config.settings import settings
from ..utils.logger import get_logger


class RedisConnectionError(Exception):
    """Custom exception for Redis connection errors."""
    pass


class RedisOperationError(Exception):
    """Custom exception for Redis operation errors."""
    pass


class RedisClient:
    """
    Async Redis client with connection management and health monitoring.
    
    This class provides a high-level interface to Redis with automatic
    connection pooling, health checks, retry logic, and comprehensive
    error handling for K-line data operations.
    """
    
    def __init__(self):
        """Initialize Redis client with connection pool."""
        self.logger = get_logger(self.__class__.__name__)
        self._pool: Optional[ConnectionPool] = None
        self._redis: Optional[Redis] = None
        self._is_connected: bool = False
        self._health_check_task: Optional[asyncio.Task] = None
        self._connection_attempts: int = 0
        self._last_health_check: Optional[datetime] = None
        
        # Configuration
        self.max_connections = 20
        self.max_retries = settings.max_retries
        self.retry_delay = 1.0  # seconds
        self.health_check_interval = 30.0  # seconds
        self.connection_timeout = 10.0  # seconds
        self.socket_timeout = 5.0  # seconds
    
    async def connect(self) -> None:
        """
        Establish connection to Redis server with retry logic.
        
        Raises:
            RedisConnectionError: If connection fails after max retries
        """
        if self._is_connected:
            self.logger.debug("Redis client already connected")
            return
        
        for attempt in range(1, self.max_retries + 1):
            try:
                self.logger.info(f"Connecting to Redis (attempt {attempt}/{self.max_retries})")
                
                # Create connection pool
                self._pool = ConnectionPool.from_url(
                    url=settings.get_redis_url(),
                    max_connections=self.max_connections,
                    socket_connect_timeout=self.connection_timeout,
                    socket_timeout=self.socket_timeout,
                    decode_responses=True,
                    health_check_interval=self.health_check_interval
                )
                
                # Create Redis client
                self._redis = Redis(connection_pool=self._pool)
                
                # Test connection
                await self._redis.ping()
                
                self._is_connected = True
                self._connection_attempts = attempt
                self.logger.info("Successfully connected to Redis")
                
                # Start health check task
                self._start_health_check()
                
                return
                
            except (ConnectionError, TimeoutError, OSError) as e:
                self.logger.warning(f"Redis connection attempt {attempt} failed: {e}")
                
                if self._pool:
                    await self._pool.disconnect()
                    self._pool = None
                
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                    self.logger.info(f"Retrying in {delay:.1f} seconds...")
                    await asyncio.sleep(delay)
                else:
                    error_msg = f"Failed to connect to Redis after {self.max_retries} attempts"
                    self.logger.error(error_msg)
                    raise RedisConnectionError(error_msg) from e
    
    async def disconnect(self) -> None:
        """Gracefully disconnect from Redis server."""
        if not self._is_connected:
            return
        
        try:
            # Stop health check task
            if self._health_check_task and not self._health_check_task.done():
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass
            
            # Close Redis connection
            if self._redis:
                await self._redis.aclose()
                self._redis = None
            
            # Disconnect connection pool
            if self._pool:
                await self._pool.disconnect()
                self._pool = None
            
            self._is_connected = False
            self.logger.info("Disconnected from Redis")
            
        except Exception as e:
            self.logger.error(f"Error during Redis disconnection: {e}")
    
    async def close(self) -> None:
        """Close the Redis connection (alias for disconnect)."""
        await self.disconnect()
    
    def _start_health_check(self) -> None:
        """Start periodic health check task."""
        if self._health_check_task and not self._health_check_task.done():
            return  # Health check already running
        
        async def health_check_loop():
            while self._is_connected:
                try:
                    await asyncio.sleep(self.health_check_interval)
                    if self._is_connected:
                        await self._health_check()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Health check error: {e}")
        
        self._health_check_task = asyncio.create_task(health_check_loop())
    
    async def _health_check(self) -> None:
        """Perform health check on Redis connection."""
        try:
            if not self._redis:
                return
            
            # Simple ping test
            await self._redis.ping()
            self._last_health_check = datetime.utcnow()
            self.logger.debug("Redis health check passed")
            
        except Exception as e:
            self.logger.warning(f"Redis health check failed: {e}")
            self._is_connected = False
            
            # Attempt reconnection
            try:
                await self.connect()
            except RedisConnectionError:
                self.logger.error("Failed to reconnect during health check")
    
    @contextlib.asynccontextmanager
    async def _get_connection(self) -> AsyncIterator[Redis]:
        """Get Redis connection with automatic retry and error handling."""
        if not self._is_connected:
            await self.connect()
        
        if not self._redis:
            raise RedisConnectionError("Redis client not initialized")
        
        try:
            yield self._redis
        except (ConnectionError, TimeoutError) as e:
            self.logger.warning(f"Redis operation failed: {e}")
            raise RedisOperationError(f"Redis operation failed") from e
    
    async def ping(self) -> bool:
        """
        Test Redis connection.
        
        Returns:
            True if ping successful, False otherwise
        """
        try:
            async with self._get_connection() as redis_conn:
                await redis_conn.ping()
                return True
        except Exception as e:
            self.logger.error(f"Redis ping failed: {e}")
            return False
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """
        Set key-value pair in Redis.
        
        Args:
            key: Redis key
            value: Value to store (will be JSON serialized if not string)
            expire: Expiration time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            async with self._get_connection() as redis_conn:
                # Serialize value if not string
                if not isinstance(value, str):
                    value = json.dumps(value, default=str)
                
                if expire:
                    result = await redis_conn.setex(key, expire, value)
                else:
                    result = await redis_conn.set(key, value)
                
                return bool(result)
                
        except Exception as e:
            self.logger.error(f"Redis SET operation failed for key '{key}': {e}")
            return False
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value by key from Redis.
        
        Args:
            key: Redis key
            default: Default value if key not found
            
        Returns:
            Value from Redis or default
        """
        try:
            async with self._get_connection() as redis_conn:
                value = await redis_conn.get(key)
                
                if value is None:
                    return default
                
                # Try to deserialize JSON
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
                    
        except Exception as e:
            self.logger.error(f"Redis GET operation failed for key '{key}': {e}")
            return default
    
    async def delete(self, *keys: str) -> int:
        """
        Delete keys from Redis.
        
        Args:
            keys: Keys to delete
            
        Returns:
            Number of keys deleted
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.delete(*keys)
                
        except Exception as e:
            self.logger.error(f"Redis DELETE operation failed for keys {keys}: {e}")
            return 0

    async def del_keys_by_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching a pattern (SCAN-based).
        Args:
            pattern: Glob-style pattern, e.g., 'kline:sync:done:*'
        Returns:
            Number of deleted keys
        """
        try:
            async with self._get_connection() as redis_conn:
                total = 0
                async for key in redis_conn.scan_iter(match=pattern, count=1000):
                    try:
                        deleted = await redis_conn.delete(key)
                        total += int(deleted or 0)
                    except Exception as e:
                        self.logger.warning(f"Failed to delete key '{key}': {e}")
                self.logger.info(f"Deleted {total} keys by pattern '{pattern}'")
                return total
        except Exception as e:
            self.logger.error(f"Redis delete by pattern failed for '{pattern}': {e}")
            return 0

    async def set_flag(self, key: str, value: int, ttl: Optional[int] = None) -> bool:
        """
        Set an integer flag (0/1) to Redis with optional TTL.
        """
        try:
            async with self._get_connection() as redis_conn:
                if ttl is not None:
                    await redis_conn.setex(key, ttl, str(int(value)))
                else:
                    await redis_conn.set(key, str(int(value)))
                return True
        except Exception as e:
            self.logger.error(f"Redis set_flag failed for '{key}': {e}")
            return False

    async def get_flag(self, key: str) -> Optional[int]:
        """
        Get integer flag, returns 0/1 or None if missing.
        """
        try:
            async with self._get_connection() as redis_conn:
                val = await redis_conn.get(key)
                if val is None:
                    return None
                try:
                    return int(val)
                except Exception:
                    # try JSON or default fallback
                    try:
                        loaded = json.loads(val)
                        if isinstance(loaded, (int, float)):
                            return int(loaded)
                        if isinstance(loaded, str) and loaded.isdigit():
                            return int(loaded)
                    except Exception:
                        pass
                    return None
        except Exception as e:
            self.logger.error(f"Redis get_flag failed for '{key}': {e}")
            return None
    
    async def exists(self, *keys: str) -> int:
        """
        Check if keys exist in Redis.
        
        Args:
            keys: Keys to check
            
        Returns:
            Number of existing keys
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.exists(*keys)
                
        except Exception as e:
            self.logger.error(f"Redis EXISTS operation failed for keys {keys}: {e}")
            return 0
    
    async def expire(self, key: str, seconds: int) -> bool:
        """
        Set expiration time for key.
        
        Args:
            key: Redis key
            seconds: Expiration time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.expire(key, seconds)
                
        except Exception as e:
            self.logger.error(f"Redis EXPIRE operation failed for key '{key}': {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """
        Get time to live for key.
        
        Args:
            key: Redis key
            
        Returns:
            TTL in seconds (-1 if no expiry, -2 if key doesn't exist)
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.ttl(key)
                
        except Exception as e:
            self.logger.error(f"Redis TTL operation failed for key '{key}': {e}")
            return -2
    
    async def zadd(self, key: str, mapping: Dict[str, float], **kwargs) -> int:
        """
        Add members to sorted set.
        
        Args:
            key: Sorted set key
            mapping: Dict of member -> score
            kwargs: Additional arguments (nx, xx, ch, incr)
            
        Returns:
            Number of elements added
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zadd(key, mapping, **kwargs)
                
        except Exception as e:
            self.logger.error(f"Redis ZADD operation failed for key '{key}': {e}")
            return 0
    
    async def zrange(self, key: str, start: int, end: int, 
                    withscores: bool = False, score_cast_func: Any = float) -> List:
        """
        Get range of members from sorted set.
        
        Args:
            key: Sorted set key
            start: Start index
            end: End index
            withscores: Include scores in result
            score_cast_func: Function to cast scores
            
        Returns:
            List of members (and scores if withscores=True)
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zrange(
                    key, start, end, 
                    withscores=withscores, 
                    score_cast_func=score_cast_func
                )
                
        except Exception as e:
            self.logger.error(f"Redis ZRANGE operation failed for key '{key}': {e}")
            return []
    
    async def zrangebyscore(self, key: str, min_score: float, max_score: float,
                           start: Optional[int] = None, num: Optional[int] = None,
                           withscores: bool = False, score_cast_func: Any = float) -> List:
        """
        Get range of members from sorted set by score.
        
        Args:
            key: Sorted set key
            min_score: Minimum score
            max_score: Maximum score
            start: Start offset
            num: Number of elements to return
            withscores: Include scores in result
            score_cast_func: Function to cast scores
            
        Returns:
            List of members (and scores if withscores=True)
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zrangebyscore(
                    key, min_score, max_score,
                    start=start, num=num,
                    withscores=withscores,
                    score_cast_func=score_cast_func
                )
                
        except Exception as e:
            self.logger.error(f"Redis ZRANGEBYSCORE operation failed for key '{key}': {e}")
            return []
    
    async def zrem(self, key: str, *members: str) -> int:
        """
        Remove members from sorted set.
        
        Args:
            key: Sorted set key
            members: Members to remove
            
        Returns:
            Number of members removed
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zrem(key, *members)
                
        except Exception as e:
            self.logger.error(f"Redis ZREM operation failed for key '{key}': {e}")
            return 0
    
    async def zcard(self, key: str) -> int:
        """
        Get cardinality (number of members) of sorted set.
        
        Args:
            key: Sorted set key
            
        Returns:
            Number of members in sorted set
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zcard(key)
                
        except Exception as e:
            self.logger.error(f"Redis ZCARD operation failed for key '{key}': {e}")
            return 0
    
    async def publish(self, channel: str, message: Any) -> int:
        """
        Publish message to channel.
        
        Args:
            channel: Channel name
            message: Message to publish
            
        Returns:
            Number of subscribers that received the message
        """
        try:
            async with self._get_connection() as redis_conn:
                # Serialize message if not string
                if not isinstance(message, str):
                    message = json.dumps(message, default=str)
                
                return await redis_conn.publish(channel, message)
                
        except Exception as e:
            self.logger.error(f"Redis PUBLISH operation failed for channel '{channel}': {e}")
            return 0
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """
        Get keys matching pattern.
        
        Args:
            pattern: Key pattern (default: all keys)
            
        Returns:
            List of matching keys
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.keys(pattern)
                
        except Exception as e:
            self.logger.error(f"Redis KEYS operation failed for pattern '{pattern}': {e}")
            return []
    
    async def zremrangebyrank(self, key: str, start: int, stop: int) -> int:
        """
        Remove elements from a sorted set by rank (index).
        
        Args:
            key: Sorted set key
            start: Start rank (0-based, inclusive)
            stop: Stop rank (0-based, inclusive)
            
        Returns:
            Number of elements removed
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zremrangebyrank(key, start, stop)
                
        except Exception as e:
            self.logger.error(f"Redis ZREMRANGEBYRANK operation failed for key '{key}': {e}")
            return 0
    
    async def zremrangebyscore(self, key: str, min_score: Union[float, str], max_score: Union[float, str]) -> int:
        """
        Remove elements from a sorted set by score range.
        
        Args:
            key: Sorted set key
            min_score: Minimum score (inclusive) or "-inf"
            max_score: Maximum score (inclusive) or "+inf"
            
        Returns:
            Number of elements removed
        """
        try:
            async with self._get_connection() as redis_conn:
                return await redis_conn.zremrangebyscore(key, min_score, max_score)
                
        except Exception as e:
            self.logger.error(f"Redis ZREMRANGEBYSCORE operation failed for key '{key}': {e}")
            return 0
    
    async def flushdb(self) -> bool:
        """
        Flush current database.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            async with self._get_connection() as redis_conn:
                await redis_conn.flushdb()
                return True
                
        except Exception as e:
            self.logger.error(f"Redis FLUSHDB operation failed: {e}")
            return False
    
    async def info(self, section: Optional[str] = None) -> Dict[str, Any]:
        """
        Get Redis server information.
        
        Args:
            section: Specific info section
            
        Returns:
            Dictionary with server information
        """
        try:
            async with self._get_connection() as redis_conn:
                info_data = await redis_conn.info(section)
                return dict(info_data) if info_data else {}
                
        except Exception as e:
            self.logger.error(f"Redis INFO operation failed: {e}")
            return {}
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        Get connection statistics.
        
        Returns:
            Dictionary with connection statistics
        """
        return {
            'is_connected': self._is_connected,
            'connection_attempts': self._connection_attempts,
            'last_health_check': self._last_health_check.isoformat() if self._last_health_check else None,
            'pool_created_connections': self._pool.created_connections if self._pool else 0,
            'pool_available_connections': len(self._pool._available_connections) if self._pool else 0,
            'pool_in_use_connections': len(self._pool._in_use_connections) if self._pool else 0,
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()


# Global Redis client instance
redis_client = RedisClient()
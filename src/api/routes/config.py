"""
Configuration endpoints for dynamic UI configuration.
"""

from fastapi import APIRouter
from typing import List, Dict, Any
from pydantic import BaseModel

from kline_processor.config.settings import settings
from kline_processor.utils.logger import get_logger

router = APIRouter()
logger = get_logger("api.config")


class IntervalConfig(BaseModel):
    """Interval configuration for TradingView."""
    value: str
    label: str
    api_interval: str


class ConfigResponse(BaseModel):
    """Configuration response model."""
    symbols: List[str]
    intervals: List[IntervalConfig]


@router.get("/config", response_model=ConfigResponse)
async def get_configuration() -> ConfigResponse:
    """
    Get supported symbols and intervals from configuration.
    
    Returns:
        Dictionary containing supported symbols and intervals
    """
    # Get symbols from settings
    symbols = settings.symbols
    
    # Get intervals - combine base interval with aggregate intervals
    intervals = [settings.base_interval]
    if settings.aggregate_intervals:
        intervals.extend(settings.aggregate_intervals)
    
    # Map intervals to TradingView format
    interval_mapping = {
        '1m': '1',
        '3m': '3', 
        '5m': '5',
        '15m': '15',
        '30m': '30',
        '1h': '60',
        '2h': '120',
        '4h': '240',
        '6h': '360',
        '8h': '480',
        '12h': '720',
        '1d': '1D',
        '3d': '3D',
        '1w': '1W',
        '1M': '1M'
    }
    
    # Convert to TradingView format
    tv_intervals = []
    for interval in intervals:
        if interval in interval_mapping:
            tv_intervals.append({
                'value': interval_mapping[interval],
                'label': interval.upper(),
                'api_interval': interval
            })
    
    logger.info(f"Returning config: {len(symbols)} symbols, {len(tv_intervals)} intervals")
    
    return ConfigResponse(
        symbols=symbols,
        intervals=[IntervalConfig(**interval) for interval in tv_intervals]
    )
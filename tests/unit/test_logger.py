"""
Unit tests for logging configuration.
"""

import pytest
from unittest.mock import patch, MagicMock
import logging
from io import StringIO
import sys

from kline_processor.utils.logger import setup_logging, get_logger, LoggerMixin, add_context


class TestLogging:
    """Test cases for logging configuration."""
    
    def test_setup_logging_debug_level(self):
        """Test logging setup with DEBUG level."""
        with patch('kline_processor.utils.logger.settings') as mock_settings:
            mock_settings.log_level = "DEBUG"
            logger = setup_logging()
            assert logger is not None
    
    def test_setup_logging_production_level(self):
        """Test logging setup with ERROR level (production)."""
        with patch('kline_processor.utils.logger.settings') as mock_settings:
            mock_settings.log_level = "ERROR"
            logger = setup_logging()
            assert logger is not None
    
    def test_get_logger_with_name(self):
        """Test getting logger with specific name."""
        logger = get_logger("test_logger")
        assert logger is not None
    
    def test_get_logger_without_name(self):
        """Test getting logger without name."""
        logger = get_logger()
        assert logger is not None
    
    def test_add_context(self):
        """Test context addition function."""
        context = add_context(symbol="BTCUSDT", timeframe="1m")
        expected = {"symbol": "BTCUSDT", "timeframe": "1m"}
        assert context == expected
    
    def test_logger_mixin(self):
        """Test LoggerMixin functionality."""
        class TestClass(LoggerMixin):
            def __init__(self):
                super().__init__()
        
        instance = TestClass()
        assert hasattr(instance, 'logger')
        assert hasattr(instance, 'log_info')
        assert hasattr(instance, 'log_warning')
        assert hasattr(instance, 'log_error')
        assert hasattr(instance, 'log_debug')
    
    def test_logger_mixin_methods(self):
        """Test LoggerMixin method calls."""
        class TestClass(LoggerMixin):
            def __init__(self):
                super().__init__()
        
        instance = TestClass()
        
        # Mock the logger to verify method calls
        with patch.object(instance, 'logger') as mock_logger:
            instance.log_info("test info", symbol="BTCUSDT")
            mock_logger.info.assert_called_once_with("test info", symbol="BTCUSDT")
            
            instance.log_warning("test warning", timeframe="1m")
            mock_logger.warning.assert_called_once_with("test warning", timeframe="1m")
            
            instance.log_error("test error", error_code=500)
            mock_logger.error.assert_called_once_with("test error", error_code=500)
            
            instance.log_debug("test debug", count=10)
            mock_logger.debug.assert_called_once_with("test debug", count=10)
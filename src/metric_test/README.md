# 技术指标测试工具

这个目录包含用于计算和测试技术指标的Python脚本。

## 文件说明

- `indicator_calculator.py` - 主要的指标计算器，从Redis获取实时K线数据
- `simple_indicator_test.py` - 简化版测试脚本，使用模拟数据，不依赖Redis
- `requirements.txt` - Python依赖包列表

## 使用方法

### 1. 使用模拟数据测试（推荐先测试）

```bash
cd src/metric_test
python simple_indicator_test.py
```

### 2. 使用真实数据（需要Redis服务运行）

```bash
# 安装依赖
pip install -r requirements.txt

# 运行指标计算
python indicator_calculator.py
```

## 硬编码参数

在脚本的`main()`函数中配置：

```python
SYMBOL = 'BTCUSDT'      # 交易对
INTERVAL = '1m'         # K线周期 (1m, 5m, 15m, 30m, 1h, 4h, 1d)
BARS_COUNT = 100        # 获取K线根数
```

## 计算的指标

1. **移动平均线**
   - MA5, MA10, MA20, MA30
   - EMA12, EMA26

2. **震荡指标**
   - RSI14 (相对强弱指数)
   - Stochastic (KDJ随机指标)

3. **趋势指标**
   - MACD (移动平均收敛/发散)
   - Bollinger Bands (布林带)

4. **波动性指标**
   - ATR14 (平均真实波幅)

5. **成交量指标**
   - OBV (能量潮)

## 输出格式

结果保存为CSV文件，位于`output/`目录下，文件名格式：
`{交易对}_{周期}_indicators_{时间戳}.csv`

## CSV文件包含的列

- timestamp - 时间戳
- open, high, low, close, volume - OHLCV原始数据
- MA5, MA10, MA20, MA30 - 移动平均线
- EMA12, EMA26 - 指数移动平均线
- RSI14 - RSI指标
- MACD, MACD_Signal, MACD_Histogram - MACD指标
- BB_Upper, BB_Middle, BB_Lower - 布林带
- Stoch_K, Stoch_D, Stoch_J - KDJ指标
- ATR14 - 平均真实波幅
- OBV - 能量潮指标
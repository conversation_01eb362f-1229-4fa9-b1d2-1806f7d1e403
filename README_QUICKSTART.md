# K-Line Processor 快速启动指南

## 安装步骤

### 1. 安装依赖

首先，确保你有 Python 3.11+ 和 Redis 服务器。

```bash
# 创建虚拟环境（推荐）
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate  # Windows

# 安装依赖
pip install -e .
# 或使用 uv（如果已安装）
uv pip install -e .
```

### 2. 配置环境

创建 `.env` 文件并配置：

```bash
# Redis 配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your_password  # 如果有密码
REDIS_DB=3

# 交易对配置
SYMBOLS=BTCUSDT,ETHUSDT
AGGREGATE_INTERVALS=3m,5m,15m,30m,1h,4h,1d

# API 配置（可选）
API_HOST=0.0.0.0
API_PORT=8000
API_CORS_ORIGINS=*

# 日志级别
LOG_LEVEL=INFO
```

### 3. 启动 Redis

确保 Redis 服务正在运行：

```bash
# 检查 Redis 状态
redis-cli ping
# 应该返回 PONG
```

## 运行程序

### 方式 1：原始模式（仅数据处理，无 API）

```bash
# 直接运行 K-line 处理器
python -m kline_processor

# 或使用 CLI
python main.py process
```

### 方式 2：带 API 服务器模式（推荐）

```bash
# 启动带 FastAPI 的完整服务
python main.py serve

# 开发模式（自动重载）
python main.py dev

# 自定义端口
python main.py serve --port 8080
```

### 方式 3：使用已安装的命令

```bash
# 如果通过 pip install -e . 安装
kline-processor  # 原始处理器
kline-api serve  # 带 API 的服务
```

## 验证运行状态

### 1. 检查日志输出

成功启动后，你应该看到类似的日志：

```
🚀 Starting K-line processor with settings:
📊 Base interval: 1m
🔄 Aggregate intervals: 3m, 5m, 15m, 30m, 1h, 4h, 1d
📈 Symbols: BTCUSDT, ETHUSDT
📁 Stats output: /tmp/kline_stats.json
🔗 Starting WebSocket streams for 2 subscriptions...
✅ WebSocket streams started successfully
📊 Real-time K-line processing is now active
```

### 2. 访问 API（如果启用）

```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 获取最新 K-line 数据
curl http://localhost:8000/api/v1/klines/latest/BTCUSDT/1m

# 查看 API 文档
# 浏览器打开: http://localhost:8000/api/docs
```

### 3. 访问 TradingView 界面

浏览器打开: http://localhost:8000

## 常见问题

### 1. ModuleNotFoundError

如果遇到模块导入错误，确保：
- 在项目根目录运行命令
- 已激活虚拟环境
- 已安装所有依赖

### 2. Redis 连接失败

检查：
- Redis 服务是否运行
- 连接配置是否正确
- 防火墙是否阻止连接

### 3. WebSocket 连接失败

可能原因：
- Binance API 限制
- 网络连接问题
- 配置的交易对不正确

## 停止程序

按 `Ctrl+C` 优雅关闭程序。程序会：
- 保存最终统计信息到 `/tmp/kline_stats.json`
- 关闭所有 WebSocket 连接
- 断开 Redis 连接

## 下一步

1. **监控**: 查看 `/tmp/kline_stats.json` 了解运行统计
2. **扩展**: 添加更多交易对到 `SYMBOLS` 环境变量
3. **集成**: 使用 API 接入你的交易系统或分析工具
4. **部署**: 使用 systemd 或 Docker 进行生产部署
"""
FastAPI application factory for K-line processor API.

This module creates and configures the FastAPI application with all
necessary routes, middleware, and static file serving.
"""

from typing import Optional
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from pathlib import Path

from .lifespan import lifespan_manager
from .routes import klines, health, websocket, config
from .middleware import setup_middleware
from kline_processor.config.settings import Settings


def create_app(settings: Optional[Settings] = None) -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Args:
        settings: Application settings (defaults to loading from environment)
        
    Returns:
        Configured FastAPI application instance
    """
    
    # Load settings if not provided
    if settings is None:
        settings = Settings()
    
    # Create FastAPI app with lifespan management
    app = FastAPI(
        title="K-Line Processor API",
        description="Real-time cryptocurrency K-line data API with WebSocket support",
        version="1.0.0",
        lifespan=lifespan_manager,
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json"
    )
    
    # Store settings in app state for access in lifespan
    app.state.settings = settings
    
    # Setup middleware (CORS, logging, error handling)
    setup_middleware(app, settings)
    
    # Include API routes
    app.include_router(
        health.router,
        prefix="/api/v1",
        tags=["health"]
    )
    
    app.include_router(
        klines.router,
        prefix="/api/v1",
        tags=["klines"]
    )
    
    app.include_router(
        websocket.router,
        prefix="/ws/v1",
        tags=["websocket"]
    )
    
    app.include_router(
        config.router,
        prefix="/api/v1",
        tags=["config"]
    )
    
    # Mount static files for TradingView web interface
    web_path = Path(settings.api_static_path if hasattr(settings, 'api_static_path') else 'web')
    if web_path.exists():
        app.mount(
            "/",
            StaticFiles(directory=str(web_path), html=True),
            name="web"
        )
    
    return app
"""
Integration tests for the main K-line processor application.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from kline_processor.main import KlineProcessor
from kline_processor.config.settings import Settings
from kline_processor.services.websocket_stream_service import StreamSubscription


@pytest.fixture
def test_settings():
    """Create test settings."""
    return Settings(
        environment="test",
        debug=True,
        redis_host="localhost",
        redis_port=6379,
        redis_db=1,
        redis_password=None,
        exchange_api_base_url="https://api.test.com",
        websocket_base_url="wss://stream.test.com",
        historical_batch_size=100,
        stream_buffer_size=10
    )


@pytest.fixture
def mock_redis_client():
    """Create mock Redis client."""
    mock = AsyncMock()
    mock.connect.return_value = None
    mock.close.return_value = None
    mock.ping.return_value = True
    return mock


@pytest.fixture
def mock_api_client():
    """Create mock API client."""
    mock = AsyncMock()
    mock.close.return_value = None
    return mock


@pytest.fixture
def mock_websocket_manager():
    """Create mock WebSocket manager."""
    mock = AsyncMock()
    mock.disconnect.return_value = None
    return mock


class TestKlineProcessor:
    """Test cases for KlineProcessor main application."""
    
    def test_initialization(self, test_settings):
        """Test application initialization."""
        app = KlineProcessor(test_settings)
        
        assert app.settings == test_settings
        assert not app.is_running
        assert app._redis_client is None
        assert app._storage is None
        assert app._historical_service is None
    
    @pytest.mark.asyncio
    async def test_context_manager(self, test_settings):
        """Test using application as async context manager."""
        with patch.object(KlineProcessor, 'start') as mock_start:
            with patch.object(KlineProcessor, 'stop') as mock_stop:
                async with KlineProcessor(test_settings) as app:
                    assert isinstance(app, KlineProcessor)
                
                mock_start.assert_called_once()
                mock_stop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, test_settings, mock_redis_client, mock_api_client, mock_websocket_manager):
        """Test successful application initialization."""
        app = KlineProcessor(test_settings)
        
        with patch('kline_processor.main.RedisClient', return_value=mock_redis_client):
            with patch('kline_processor.main.ExchangeAPIClient', return_value=mock_api_client):
                with patch('kline_processor.main.WebSocketManager', return_value=mock_websocket_manager):
                    with patch('kline_processor.main.KlineStorage') as mock_storage_class:
                        with patch('kline_processor.main.HistoricalDataService') as mock_historical_class:
                            with patch('kline_processor.main.KlineAggregatorService') as mock_aggregator_class:
                                with patch('kline_processor.main.WebSocketStreamService') as mock_stream_class:
                                    
                                    await app.initialize()
                                    
                                    # Verify components were created
                                    assert app._redis_client == mock_redis_client
                                    assert app._api_client == mock_api_client
                                    assert app._websocket_manager == mock_websocket_manager
                                    
                                    # Verify Redis connection was established
                                    mock_redis_client.connect.assert_called_once()
                                    
                                    # Verify services were created
                                    mock_storage_class.assert_called_once()
                                    mock_historical_class.assert_called_once()
                                    mock_aggregator_class.assert_called_once()
                                    mock_stream_class.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_redis_failure(self, test_settings, mock_redis_client):
        """Test initialization with Redis connection failure."""
        app = KlineProcessor(test_settings)
        mock_redis_client.connect.side_effect = Exception("Redis connection failed")
        
        with patch('kline_processor.main.RedisClient', return_value=mock_redis_client):
            with pytest.raises(Exception, match="Redis connection failed"):
                await app.initialize()
    
    @pytest.mark.asyncio
    async def test_start_success(self, test_settings):
        """Test successful application start."""
        app = KlineProcessor(test_settings)
        
        # Mock all components
        app._redis_client = AsyncMock()
        app._historical_service = AsyncMock()
        app._aggregator_service = AsyncMock()
        app._stream_service = AsyncMock()
        
        await app.start()
        
        assert app.is_running
        app._historical_service.start.assert_called_once()
        app._aggregator_service.__aenter__.assert_called_once()
        app._stream_service.start.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_already_running(self, test_settings):
        """Test starting application when already running."""
        app = KlineProcessor(test_settings)
        app._is_running = True
        
        # Should not raise exception
        await app.start()
        assert app.is_running
    
    @pytest.mark.asyncio
    async def test_start_service_failure(self, test_settings):
        """Test application start with service failure."""
        app = KlineProcessor(test_settings)
        
        # Mock components with failure
        app._redis_client = AsyncMock()
        app._historical_service = AsyncMock()
        app._historical_service.start.side_effect = Exception("Service start failed")
        
        with pytest.raises(Exception, match="Service start failed"):
            await app.start()
        
        assert not app.is_running
    
    @pytest.mark.asyncio
    async def test_stop_success(self, test_settings):
        """Test successful application stop."""
        app = KlineProcessor(test_settings)
        app._is_running = True
        
        # Mock all components
        app._stream_service = AsyncMock()
        app._aggregator_service = AsyncMock()
        app._historical_service = AsyncMock()
        app._websocket_manager = AsyncMock()
        app._api_client = AsyncMock()
        app._redis_client = AsyncMock()
        
        await app.stop()
        
        assert not app.is_running
        app._stream_service.stop.assert_called_once()
        app._aggregator_service.__aexit__.assert_called_once()
        app._historical_service.stop.assert_called_once()
        app._websocket_manager.disconnect.assert_called_once()
        app._api_client.close.assert_called_once()
        app._redis_client.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_stop_not_running(self, test_settings):
        """Test stopping application when not running."""
        app = KlineProcessor(test_settings)
        
        # Should not raise exception
        await app.stop()
        assert not app.is_running
    
    @pytest.mark.asyncio
    async def test_fetch_historical_data_success(self, test_settings):
        """Test successful historical data fetching."""
        app = KlineProcessor(test_settings)
        
        # Mock historical service
        mock_result = {
            'success': True,
            'symbol': 'BTCUSDT',
            'interval': '1m',
            'klines_fetched': 100,
            'duration_seconds': 1.5
        }
        app._historical_service = AsyncMock()
        app._historical_service.fetch_klines.return_value = mock_result
        
        result = await app.fetch_historical_data(
            symbol='BTCUSDT',
            interval='1m',
            start_time=1640995200000,
            end_time=1640998800000
        )
        
        assert result == mock_result
        app._historical_service.fetch_klines.assert_called_once_with(
            symbol='BTCUSDT',
            interval='1m',
            start_time=1640995200000,
            end_time=1640998800000,
            limit=None
        )
    
    @pytest.mark.asyncio
    async def test_fetch_historical_data_not_initialized(self, test_settings):
        """Test fetching historical data when service not initialized."""
        app = KlineProcessor(test_settings)
        
        with pytest.raises(RuntimeError, match="Historical service not initialized"):
            await app.fetch_historical_data('BTCUSDT', '1m')
    
    @pytest.mark.asyncio
    async def test_subscribe_to_streams_success(self, test_settings):
        """Test successful stream subscription."""
        app = KlineProcessor(test_settings)
        
        # Mock stream service
        app._stream_service = AsyncMock()
        app._stream_service.subscribe_to_streams.return_value = True
        
        subscriptions = [
            StreamSubscription(symbol='BTCUSDT', interval='1m'),
            StreamSubscription(symbol='ETHUSDT', interval='1m')
        ]
        
        result = await app.subscribe_to_streams(subscriptions)
        
        assert result is True
        app._stream_service.subscribe_to_streams.assert_called_once_with(subscriptions)
    
    @pytest.mark.asyncio
    async def test_subscribe_to_streams_not_initialized(self, test_settings):
        """Test subscribing to streams when service not initialized."""
        app = KlineProcessor(test_settings)
        
        with pytest.raises(RuntimeError, match="Stream service not initialized"):
            await app.subscribe_to_streams([])
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_success(self, test_settings):
        """Test successful K-line aggregation."""
        app = KlineProcessor(test_settings)
        
        # Mock aggregator service result
        from kline_processor.services.kline_aggregator_service import AggregationResult
        mock_result = AggregationResult(
            symbol='BTCUSDT',
            from_interval='1m',
            to_interval='5m',
            klines_processed=5,
            klines_generated=1,
            time_range=(1640995200000, 1640995500000),
            duration_seconds=1.2,
            success=True
        )
        
        app._aggregator_service = AsyncMock()
        app._aggregator_service.aggregate_klines.return_value = mock_result
        
        result = await app.aggregate_klines(
            symbol='BTCUSDT',
            from_interval='1m',
            to_interval='5m',
            start_time=1640995200000,
            end_time=1640995500000
        )
        
        expected_result = {
            'success': True,
            'symbol': 'BTCUSDT',
            'from_interval': '1m',
            'to_interval': '5m',
            'klines_processed': 5,
            'klines_generated': 1,
            'duration_seconds': 1.2,
            'error': None
        }
        
        assert result == expected_result
        app._aggregator_service.aggregate_klines.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_not_initialized(self, test_settings):
        """Test aggregating K-lines when service not initialized."""
        app = KlineProcessor(test_settings)
        
        with pytest.raises(RuntimeError, match="Aggregator service not initialized"):
            await app.aggregate_klines('BTCUSDT', '1m', '5m', 1640995200000, 1640995500000)
    
    def test_get_application_statistics(self, test_settings):
        """Test getting application statistics."""
        app = KlineProcessor(test_settings)
        app._is_running = True
        
        # Mock service statistics
        app._historical_service = MagicMock()
        app._historical_service.get_statistics.return_value = {'total_requests': 10}
        
        app._aggregator_service = MagicMock()
        app._aggregator_service.get_statistics.return_value = {'total_aggregations': 5}
        
        app._stream_service = MagicMock()
        app._stream_service.get_statistics.return_value = {'total_messages': 100}
        
        stats = app.get_application_statistics()
        
        assert stats['application']['is_running'] is True
        assert stats['application']['settings']['log_level'] == 'INFO'
        assert stats['historical_service']['total_requests'] == 10
        assert stats['aggregator_service']['total_aggregations'] == 5
        assert stats['stream_service']['total_messages'] == 100
    
    @pytest.mark.asyncio 
    async def test_health_check_healthy(self, test_settings):
        """Test health check when all services are healthy."""
        app = KlineProcessor(test_settings)
        app._is_running = True
        
        # Mock Redis ping
        app._redis_client = AsyncMock()
        app._redis_client.ping.return_value = True
        
        # Mock stream service health
        app._stream_service = AsyncMock()
        app._stream_service.health_check.return_value = {
            'healthy': True,
            'issues': []
        }
        
        health = await app.health_check()
        
        assert health['healthy'] is True
        assert health['application_running'] is True
        assert len(health['issues']) == 0
        assert health['services']['redis']['healthy'] is True
        assert health['services']['stream']['healthy'] is True
    
    @pytest.mark.asyncio
    async def test_health_check_redis_unhealthy(self, test_settings):
        """Test health check when Redis is unhealthy."""
        app = KlineProcessor(test_settings)
        app._is_running = True
        
        # Mock Redis ping failure
        app._redis_client = AsyncMock()
        app._redis_client.ping.side_effect = Exception("Redis connection failed")
        
        app._stream_service = AsyncMock()
        app._stream_service.health_check.return_value = {'healthy': True, 'issues': []}
        
        health = await app.health_check()
        
        assert health['healthy'] is False
        assert 'Redis connection failed' in health['issues'][0]
        assert health['services']['redis']['healthy'] is False
    
    @pytest.mark.asyncio
    async def test_health_check_stream_unhealthy(self, test_settings):
        """Test health check when stream service is unhealthy."""
        app = KlineProcessor(test_settings)
        app._is_running = True
        
        app._redis_client = AsyncMock()
        app._redis_client.ping.return_value = True
        
        # Mock stream service unhealthy
        app._stream_service = AsyncMock()
        app._stream_service.health_check.return_value = {
            'healthy': False,
            'issues': ['Service not running']
        }
        
        health = await app.health_check()
        
        assert health['healthy'] is False
        assert 'Service not running' in health['issues']
        assert health['services']['stream']['healthy'] is False
    
    @pytest.mark.asyncio
    async def test_run_with_shutdown_signal(self, test_settings):
        """Test running application until shutdown signal."""
        app = KlineProcessor(test_settings)
        
        # Mock start method
        with patch.object(app, 'start') as mock_start:
            with patch.object(app, 'stop') as mock_stop:
                
                # Set shutdown event after short delay
                async def set_shutdown():
                    await asyncio.sleep(0.1)
                    app._shutdown_event.set()
                
                # Run both tasks concurrently
                await asyncio.gather(
                    app.run(),
                    set_shutdown(),
                    return_exceptions=True
                )
                
                mock_start.assert_called_once()
                mock_stop.assert_called_once()
    
    def test_is_running_property(self, test_settings):
        """Test is_running property."""
        app = KlineProcessor(test_settings)
        
        assert not app.is_running
        
        app._is_running = True
        assert app.is_running
    
    def test_settings_property(self, test_settings):
        """Test settings property."""
        app = KlineProcessor(test_settings)
        
        assert app.settings == test_settings
        
        new_settings = Settings(environment="production")
        app.settings = new_settings
        assert app.settings == new_settings
"""
Unit tests for timeframe utilities.
"""

import pytest
from datetime import datetime, timezone

from kline_processor.utils.timeframe import TimeframeUtils, TimeframeUnit


class TestTimeframeUtils:
    """Test cases for TimeframeUtils class."""
    
    def test_get_timeframe_milliseconds_minutes(self):
        """Test timeframe milliseconds for minute intervals."""
        assert TimeframeUtils.get_timeframe_milliseconds("1m") == 60 * 1000
        assert TimeframeUtils.get_timeframe_milliseconds("5m") == 5 * 60 * 1000
        assert TimeframeUtils.get_timeframe_milliseconds("15m") == 15 * 60 * 1000
        assert TimeframeUtils.get_timeframe_milliseconds("30m") == 30 * 60 * 1000
    
    def test_get_timeframe_milliseconds_hours(self):
        """Test timeframe milliseconds for hour intervals."""
        assert TimeframeUtils.get_timeframe_milliseconds("1h") == 60 * 60 * 1000
        assert TimeframeUtils.get_timeframe_milliseconds("4h") == 4 * 60 * 60 * 1000
        assert TimeframeUtils.get_timeframe_milliseconds("12h") == 12 * 60 * 60 * 1000
    
    def test_get_timeframe_milliseconds_days(self):
        """Test timeframe milliseconds for day intervals."""
        assert TimeframeUtils.get_timeframe_milliseconds("1d") == 24 * 60 * 60 * 1000
        assert TimeframeUtils.get_timeframe_milliseconds("3d") == 3 * 24 * 60 * 60 * 1000
    
    def test_get_timeframe_milliseconds_weeks(self):
        """Test timeframe milliseconds for week intervals."""
        assert TimeframeUtils.get_timeframe_milliseconds("1w") == 7 * 24 * 60 * 60 * 1000
    
    def test_get_timeframe_milliseconds_months(self):
        """Test timeframe milliseconds for month intervals."""
        assert TimeframeUtils.get_timeframe_milliseconds("1M") == 30 * 24 * 60 * 60 * 1000
    
    def test_get_timeframe_milliseconds_invalid(self):
        """Test error for invalid timeframe."""
        with pytest.raises(ValueError, match="Unsupported timeframe"):
            TimeframeUtils.get_timeframe_milliseconds("2m")
        
        with pytest.raises(ValueError, match="Unsupported timeframe"):
            TimeframeUtils.get_timeframe_milliseconds("invalid")
    
    def test_align_timestamp_to_timeframe_minutes(self):
        """Test timestamp alignment for minute intervals."""
        # Test 1-minute alignment
        timestamp = 1640995230000  # 2022-01-01 00:00:30
        aligned = TimeframeUtils.align_timestamp_to_timeframe(timestamp, "1m")
        assert aligned == 1640995200000  # 2022-01-01 00:00:00
        
        # Test 5-minute alignment
        timestamp = 1640995380000  # 2022-01-01 00:03:00
        aligned = TimeframeUtils.align_timestamp_to_timeframe(timestamp, "5m")
        assert aligned == 1640995200000  # 2022-01-01 00:00:00
    
    def test_align_timestamp_to_timeframe_hours(self):
        """Test timestamp alignment for hour intervals."""
        # Test 1-hour alignment
        timestamp = 1640996400000  # 2022-01-01 00:20:00
        aligned = TimeframeUtils.align_timestamp_to_timeframe(timestamp, "1h")
        assert aligned == 1640995200000  # 2022-01-01 00:00:00
        
        # Test 4-hour alignment
        timestamp = 1641009600000  # 2022-01-01 04:00:00
        aligned = TimeframeUtils.align_timestamp_to_timeframe(timestamp, "4h")
        assert aligned == 1641009600000  # Should be aligned to 4h boundary
    
    def test_get_next_timeframe_timestamp(self):
        """Test getting next timeframe timestamp."""
        timestamp = 1640995200000  # 2022-01-01 00:00:00
        next_ts = TimeframeUtils.get_next_timeframe_timestamp(timestamp, "1m")
        assert next_ts == 1640995260000  # 2022-01-01 00:01:00
        
        next_ts = TimeframeUtils.get_next_timeframe_timestamp(timestamp, "1h")
        assert next_ts == 1640998800000  # 2022-01-01 01:00:00
    
    def test_get_previous_timeframe_timestamp(self):
        """Test getting previous timeframe timestamp."""
        timestamp = 1640995260000  # 2022-01-01 00:01:00
        prev_ts = TimeframeUtils.get_previous_timeframe_timestamp(timestamp, "1m")
        assert prev_ts == 1640995200000  # 2022-01-01 00:00:00
        
        timestamp = 1640998800000  # 2022-01-01 01:00:00
        prev_ts = TimeframeUtils.get_previous_timeframe_timestamp(timestamp, "1h")
        assert prev_ts == 1640995200000  # 2022-01-01 00:00:00
    
    def test_is_valid_timeframe(self):
        """Test timeframe validation."""
        assert TimeframeUtils.is_valid_timeframe("1m") is True
        assert TimeframeUtils.is_valid_timeframe("5m") is True
        assert TimeframeUtils.is_valid_timeframe("1h") is True
        assert TimeframeUtils.is_valid_timeframe("1d") is True
        assert TimeframeUtils.is_valid_timeframe("1w") is True
        assert TimeframeUtils.is_valid_timeframe("1M") is True
        
        assert TimeframeUtils.is_valid_timeframe("2m") is False
        assert TimeframeUtils.is_valid_timeframe("invalid") is False
        assert TimeframeUtils.is_valid_timeframe("") is False
    
    def test_get_supported_timeframes(self):
        """Test getting supported timeframes."""
        timeframes = TimeframeUtils.get_supported_timeframes()
        assert isinstance(timeframes, list)
        assert len(timeframes) > 0
        assert "1m" in timeframes
        assert "1h" in timeframes
        assert "1d" in timeframes
        assert "1w" in timeframes
        assert "1M" in timeframes
    
    def test_parse_timeframe_minutes(self):
        """Test parsing minute timeframes."""
        number, unit = TimeframeUtils.parse_timeframe("5m")
        assert number == 5
        assert unit == TimeframeUnit.MINUTE
    
    def test_parse_timeframe_hours(self):
        """Test parsing hour timeframes."""
        number, unit = TimeframeUtils.parse_timeframe("1h")
        assert number == 1
        assert unit == TimeframeUnit.HOUR
        
        number, unit = TimeframeUtils.parse_timeframe("12h")
        assert number == 12
        assert unit == TimeframeUnit.HOUR
    
    def test_parse_timeframe_days(self):
        """Test parsing day timeframes."""
        number, unit = TimeframeUtils.parse_timeframe("1d")
        assert number == 1
        assert unit == TimeframeUnit.DAY
        
        number, unit = TimeframeUtils.parse_timeframe("3d")
        assert number == 3
        assert unit == TimeframeUnit.DAY
    
    def test_parse_timeframe_weeks(self):
        """Test parsing week timeframes."""
        number, unit = TimeframeUtils.parse_timeframe("1w")
        assert number == 1
        assert unit == TimeframeUnit.WEEK
    
    def test_parse_timeframe_months(self):
        """Test parsing month timeframes."""
        number, unit = TimeframeUtils.parse_timeframe("1M")
        assert number == 1
        assert unit == TimeframeUnit.MONTH
    
    def test_parse_timeframe_invalid(self):
        """Test parsing invalid timeframes."""
        with pytest.raises(ValueError, match="Invalid timeframe format"):
            TimeframeUtils.parse_timeframe("invalid")
        
        with pytest.raises(ValueError, match="Invalid timeframe format"):
            TimeframeUtils.parse_timeframe("1x")
        
        with pytest.raises(ValueError, match="Invalid timeframe format"):
            TimeframeUtils.parse_timeframe("")
    
    def test_format_timeframe(self):
        """Test formatting timeframes."""
        assert TimeframeUtils.format_timeframe(5, TimeframeUnit.MINUTE) == "5m"
        assert TimeframeUtils.format_timeframe(1, TimeframeUnit.HOUR) == "1h"
        assert TimeframeUtils.format_timeframe(1, TimeframeUnit.DAY) == "1d"
        assert TimeframeUtils.format_timeframe(1, TimeframeUnit.WEEK) == "1w"
        assert TimeframeUtils.format_timeframe(1, TimeframeUnit.MONTH) == "1M"
    
    def test_get_timeframe_hierarchy(self):
        """Test getting timeframe hierarchy."""
        hierarchy = TimeframeUtils.get_timeframe_hierarchy()
        assert isinstance(hierarchy, dict)
        
        # Check that 1m can aggregate to higher timeframes
        assert "1m" in hierarchy
        higher_from_1m = hierarchy["1m"]
        assert "5m" in higher_from_1m
        assert "1h" in higher_from_1m
        assert "1d" in higher_from_1m
        
        # Check that 5m can aggregate to some higher timeframes
        assert "5m" in hierarchy
        higher_from_5m = hierarchy["5m"]
        assert "15m" in higher_from_5m
        assert "30m" in higher_from_5m
        assert "1h" in higher_from_5m
    
    def test_can_aggregate_to(self):
        """Test aggregation possibility checking."""
        # Valid aggregations
        assert TimeframeUtils.can_aggregate_to("1m", "5m") is True
        assert TimeframeUtils.can_aggregate_to("1m", "1h") is True
        assert TimeframeUtils.can_aggregate_to("5m", "15m") is True
        assert TimeframeUtils.can_aggregate_to("1h", "1d") is True
        
        # Invalid aggregations (reverse)
        assert TimeframeUtils.can_aggregate_to("5m", "1m") is False
        assert TimeframeUtils.can_aggregate_to("1h", "1m") is False
        assert TimeframeUtils.can_aggregate_to("1d", "1h") is False
        
        # Same timeframe
        assert TimeframeUtils.can_aggregate_to("1m", "1m") is False
        
        # Invalid timeframes
        assert TimeframeUtils.can_aggregate_to("invalid", "1m") is False
        assert TimeframeUtils.can_aggregate_to("1m", "invalid") is False
    
    def test_timestamp_to_human_readable(self):
        """Test timestamp to human readable conversion."""
        timestamp = 1640995200000  # 2022-01-01 00:00:00 UTC
        readable = TimeframeUtils.timestamp_to_human_readable(timestamp)
        assert readable == "2022-01-01 00:00:00 UTC"
    
    def test_human_readable_to_timestamp(self):
        """Test human readable to timestamp conversion."""
        datetime_str = "2022-01-01 00:00:00"
        timestamp = TimeframeUtils.human_readable_to_timestamp(datetime_str)
        assert timestamp == 1640995200000
    
    def test_human_readable_to_timestamp_invalid(self):
        """Test invalid human readable datetime."""
        with pytest.raises(ValueError, match="Invalid datetime format"):
            TimeframeUtils.human_readable_to_timestamp("invalid")
        
        with pytest.raises(ValueError, match="Invalid datetime format"):
            TimeframeUtils.human_readable_to_timestamp("2022-01-01")
    
    def test_round_trip_conversion(self):
        """Test round-trip timestamp conversion."""
        original_timestamp = 1640995200000
        readable = TimeframeUtils.timestamp_to_human_readable(original_timestamp)
        converted_back = TimeframeUtils.human_readable_to_timestamp(readable.replace(" UTC", ""))
        assert converted_back == original_timestamp


class TestTimeframeUnit:
    """Test cases for TimeframeUnit enum."""
    
    def test_enum_values(self):
        """Test enum values are correct."""
        assert TimeframeUnit.MINUTE.value == "m"
        assert TimeframeUnit.HOUR.value == "h"
        assert TimeframeUnit.DAY.value == "d"
        assert TimeframeUnit.WEEK.value == "w"
        assert TimeframeUnit.MONTH.value == "M"
    
    def test_enum_from_string(self):
        """Test creating enum from string values."""
        assert TimeframeUnit("m") == TimeframeUnit.MINUTE
        assert TimeframeUnit("h") == TimeframeUnit.HOUR
        assert TimeframeUnit("d") == TimeframeUnit.DAY
        assert TimeframeUnit("w") == TimeframeUnit.WEEK
        assert TimeframeUnit("M") == TimeframeUnit.MONTH
    
    def test_enum_invalid_value(self):
        """Test invalid enum value raises error."""
        with pytest.raises(ValueError):
            TimeframeUnit("x")
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "kline-processor"
version = "0.1.0"
description = "Cryptocurrency K-line data processing system"
authors = [
    {name = "song", email = "<EMAIL>"},
]
dependencies = [
    "httpx>=0.25.0",
    "redis[hiredis]>=5.0.0",
    "websockets>=12.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "structlog>=23.0.0",
    "click>=8.0.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "python-dotenv>=1.0.0",
]
requires-python = ">=3.11"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    "types-redis>=4.6.0",
]

[project.scripts]
kline-processor = "kline_processor.__main__:main"
kline-api = "main:cli"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
line-length = 88
target-version = "py311"

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "mypy>=1.17.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-mock>=3.14.1",
    "ruff>=0.12.5",
    "types-redis>=4.6.0.20241004",
]

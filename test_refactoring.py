#!/usr/bin/env python3
"""
Test script to validate the refactored K-line processor.

This script tests:
1. Storage layer remove-then-insert functionality
2. WebSocket subscriptions for all intervals
3. Data processing flow
4. Removal of aggregation logic
"""

import asyncio
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.kline_processor.config.settings import Settings
from src.kline_processor.storage.kline_storage import KlineStorage
from src.kline_processor.storage.redis_client import RedisClient
from src.kline_processor.models.kline import KlineData
from src.kline_processor.main import KlineProcessor


async def test_storage_remove_insert():
    """Test the remove-then-insert pattern in storage."""
    print("\n🧪 Testing Storage Remove-Then-Insert Pattern...")
    
    # Initialize storage
    redis_client = RedisClient()
    await redis_client.connect()
    storage = KlineStorage(redis_client)
    
    # Create test K-line data
    test_kline = KlineData(
        t=1700000000000,  # Test timestamp
        T=1700000059999,  # Close time
        s="BTCUSDT",
        i="1m",
        f=123456,
        L=123500,
        o="50000.00",
        c="50100.00",
        h="50150.00",
        l="49950.00",
        v="100.5",
        n=45,
        x=True,
        q="5050000.00",
        V="50.25",
        Q="2525000.00",
        B="0"
    )
    
    try:
        # Step 1: Store initial K-line
        print("  1️⃣ Storing initial K-line...")
        stored = await storage.store_kline(test_kline, publish=False)
        assert stored, "Failed to store initial K-line"
        print("     ✅ Initial K-line stored")
        
        # Step 2: Verify it exists
        klines = await storage.get_klines("BTCUSDT", "1m", start_time=test_kline.t, end_time=test_kline.t)
        assert len(klines) == 1, f"Expected 1 K-line, got {len(klines)}"
        print("     ✅ K-line verified in storage")
        
        # Step 3: Test remove by timestamp
        print("  2️⃣ Testing remove by timestamp...")
        removed = await storage.remove_kline_by_timestamp("BTCUSDT", "1m", test_kline.t)
        assert removed, "Failed to remove K-line"
        print("     ✅ K-line removed successfully")
        
        # Step 4: Verify it's gone
        klines = await storage.get_klines("BTCUSDT", "1m", start_time=test_kline.t, end_time=test_kline.t)
        assert len(klines) == 0, f"Expected 0 K-lines after removal, got {len(klines)}"
        print("     ✅ Removal verified")
        
        # Step 5: Insert new K-line at same timestamp
        print("  3️⃣ Inserting updated K-line...")
        test_kline.c = "50200.00"  # Change close price
        test_kline.h = "50250.00"  # Change high price
        stored = await storage.store_kline(test_kline, publish=False)
        assert stored, "Failed to store updated K-line"
        print("     ✅ Updated K-line stored")
        
        # Step 6: Verify update
        klines = await storage.get_klines("BTCUSDT", "1m", start_time=test_kline.t, end_time=test_kline.t)
        assert len(klines) == 1, f"Expected 1 K-line after update, got {len(klines)}"
        assert klines[0].c == "50200.00", f"Expected close price 50200.00, got {klines[0].c}"
        print("     ✅ Update verified - remove-then-insert pattern works!")
        
        return True
        
    except Exception as e:
        print(f"     ❌ Test failed: {e}")
        return False
    finally:
        await redis_client.close()


async def test_websocket_intervals():
    """Test WebSocket subscription configuration."""
    print("\n🧪 Testing WebSocket Interval Configuration...")
    
    settings = Settings()
    
    # Check WebSocket intervals are properly configured
    print(f"  📊 Configured WebSocket intervals: {settings.websocket_intervals_list}")
    
    expected_intervals = ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d"]
    
    for interval in expected_intervals:
        if interval in settings.websocket_intervals_list:
            print(f"     ✅ {interval} configured")
        else:
            print(f"     ❌ {interval} missing!")
            return False
    
    return True


async def test_aggregation_removed():
    """Test that aggregation service has been removed."""
    print("\n🧪 Testing Aggregation Service Removal...")
    
    # Check that aggregator service file doesn't exist
    aggregator_path = Path("src/kline_processor/services/kline_aggregator_service.py")
    if aggregator_path.exists():
        print("     ❌ Aggregator service file still exists!")
        return False
    else:
        print("     ✅ Aggregator service file removed")
    
    # Check that KlineProcessor doesn't have aggregator references
    try:
        processor = KlineProcessor(setup_signals=False)
        if hasattr(processor, '_aggregator_service'):
            print("     ❌ KlineProcessor still has _aggregator_service attribute!")
            return False
        else:
            print("     ✅ No aggregator service in KlineProcessor")
        
        if hasattr(processor, 'process_realtime_kline'):
            print("     ❌ KlineProcessor still has process_realtime_kline method!")
            return False
        else:
            print("     ✅ process_realtime_kline method removed")
            
    except Exception as e:
        print(f"     ❌ Error checking KlineProcessor: {e}")
        return False
    
    return True


async def test_subscription_creation():
    """Test creating subscriptions for all intervals."""
    print("\n🧪 Testing Subscription Creation...")
    
    settings = Settings()
    processor = KlineProcessor(settings, setup_signals=False)
    
    try:
        # Create subscriptions
        symbols = ["BTCUSDT", "ETHUSDT"]
        intervals = settings.websocket_intervals_list
        
        subscriptions = await processor.create_all_interval_subscriptions(symbols, intervals)
        
        expected_count = len(symbols) * len(intervals)
        actual_count = len(subscriptions)
        
        print(f"  📊 Expected subscriptions: {expected_count}")
        print(f"  📊 Actual subscriptions: {actual_count}")
        
        if actual_count == expected_count:
            print("     ✅ Correct number of subscriptions created")
        else:
            print(f"     ❌ Subscription count mismatch!")
            return False
        
        # Verify all subscriptions have auto_aggregate=False
        for sub in subscriptions:
            if sub.auto_aggregate:
                print(f"     ❌ Subscription {sub.symbol}@{sub.interval} has auto_aggregate=True!")
                return False
        
        print("     ✅ All subscriptions have auto_aggregate=False")
        
        # Verify all symbol/interval combinations exist
        for symbol in symbols:
            for interval in intervals:
                found = any(s.symbol == symbol and s.interval == interval for s in subscriptions)
                if not found:
                    print(f"     ❌ Missing subscription for {symbol}@{interval}")
                    return False
        
        print("     ✅ All symbol/interval combinations present")
        
        return True
        
    except Exception as e:
        print(f"     ❌ Test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 K-line Processor Refactoring Validation Tests")
    print("=" * 50)
    
    tests = [
        ("Storage Remove-Insert", test_storage_remove_insert),
        ("WebSocket Intervals", test_websocket_intervals),
        ("Aggregation Removal", test_aggregation_removed),
        ("Subscription Creation", test_subscription_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🏆 Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n✨ All tests passed! Refactoring is successful!")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please review the output above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
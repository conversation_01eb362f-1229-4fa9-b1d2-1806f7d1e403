#!/usr/bin/env python3
"""
Technical Indicator Calculator using Pandas
硬编码参数：交易对、K线周期、获取时间根数
计算各种技术指标并输出CSV格式
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
import redis.asyncio as redis
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class IndicatorCalculator:
    def __init__(self, symbol='BTCUSDT', interval='1m', bars_count=100):
        """
        初始化指标计算器
        
        参数:
        - symbol: 交易对 (默认: BTCUSDT)
        - interval: K线周期 (默认: 1m)
        - bars_count: 获取的K线根数 (默认: 100)
        """
        self.symbol = symbol
        self.interval = interval
        self.bars_count = bars_count
        # 从环境变量读取 Redis 配置
        self.redis_host = os.getenv('REDIS_HOST', 'localhost')
        self.redis_port = int(os.getenv('REDIS_PORT', 6379))
        self.redis_db = int(os.getenv('REDIS_DB', 0))
        self.redis_password = os.getenv('REDIS_PASSWORD', None)
        self.df = None
        
    async def get_kline_data(self):
        """从Redis获取K线数据"""
        try:
            # 创建 Redis 客户端
            client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                password=self.redis_password,
                decode_responses=False
            )
            
            # 构建Redis key
            redis_key = f"kline:{self.symbol}:{self.interval}"
            
            # 先检查有序集合中的数据总数
            total_count = await client.zcard(redis_key)
            print(f"Redis中 {redis_key} 的数据总数: {total_count}")
            
            # 获取最新的K线数据 (使用 zrange 从有序集合获取)
            # 获取最新的 bars_count 条数据 (score 从大到小)
            klines = await client.zrange(redis_key, -self.bars_count, -1)
            
            # 如果有数据，检查最新的时间戳
            if klines:
                latest_kline = klines[-1]
                if isinstance(latest_kline, bytes):
                    latest_kline = latest_kline.decode('utf-8')
                latest_data = json.loads(latest_kline)
                latest_timestamp = latest_data.get('ot', latest_data.get('t', 0))
                print(f"最新数据时间戳: {latest_timestamp} ({datetime.fromtimestamp(latest_timestamp/1000)})")
            
            if not klines:
                print(f"No data found for {self.symbol} {self.interval}")
                return None
            
            # 解析K线数据
            data = []
            for i, kline in enumerate(klines):
                try:
                    # 如果是字节类型，先解码
                    if isinstance(kline, bytes):
                        kline = kline.decode('utf-8')
                    k = json.loads(kline)
                    
                    # 调试：打印第一条和最后一条数据的结构
                    if i == 0:
                        print(f"第一条K线数据结构: {list(k.keys())}")
                    if i == len(klines) - 1:
                        print(f"最后一条K线数据时间戳: {k.get('ot', k.get('t', 0))}")
                    
                    # 检查数据格式并提取
                    if 'ot' in k:  # 新格式
                        timestamp = k['ot']
                    elif 't' in k:  # 旧格式
                        timestamp = k['t']
                    else:
                        print(f"警告：未找到时间戳字段，跳过该条数据")
                        continue
                        
                    data.append({
                        'timestamp': timestamp,
                        'open': float(k['o']),
                        'high': float(k['h']),
                        'low': float(k['l']),
                        'close': float(k['c']),
                        'volume': float(k['v'])
                    })
                except Exception as e:
                    print(f"解析第 {i+1} 条数据时出错: {e}")
                    if i == 0:
                        print(f"原始数据: {kline}")
                    continue
            
            # 关闭连接
            await client.close()
            
            # 创建DataFrame
            self.df = pd.DataFrame(data)
            # 转换时间戳为 UTC 时间
            self.df['timestamp'] = pd.to_datetime(self.df['timestamp'], unit='ms', utc=True)
            # 转换为东八区时间（北京时间）
            self.df['timestamp'] = self.df['timestamp'].dt.tz_convert('Asia/Shanghai')
            self.df.set_index('timestamp', inplace=True)
            
            return self.df
            
        except Exception as e:
            print(f"Error getting data from Redis: {e}")
            return None
    
    def calculate_ma(self, period):
        """计算简单移动平均线"""
        return self.df['close'].rolling(window=period).mean()
    
    def calculate_ema(self, period):
        """计算指数移动平均线"""
        return self.df['close'].ewm(span=period, adjust=False).mean()
    
    def calculate_rsi(self, period=14):
        """计算RSI指标"""
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = self.calculate_ema(fast)
        ema_slow = self.calculate_ema(slow)
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def calculate_bollinger_bands(self, period=20, std_dev=2):
        """计算布林带"""
        ma = self.calculate_ma(period)
        std = self.df['close'].rolling(window=period).std()
        upper_band = ma + (std * std_dev)
        lower_band = ma - (std * std_dev)
        
        # 计算当前价格相对于上下轨道的百分比
        # 高于上轨道的百分比: ((close - upper) / upper) * 100
        # 低于下轨道的百分比: ((lower - close) / lower) * 100
        above_upper_pct = ((self.df['close'] - upper_band) / upper_band) * 100
        below_lower_pct = ((lower_band - self.df['close']) / lower_band) * 100
        
        return upper_band, ma, lower_band, above_upper_pct, below_lower_pct
    
    def calculate_stochastic(self, k_period=14, d_period=3):
        """计算随机指标(KDJ)"""
        low_min = self.df['low'].rolling(window=k_period).min()
        high_max = self.df['high'].rolling(window=k_period).max()
        
        k_percent = 100 * ((self.df['close'] - low_min) / (high_max - low_min))
        d_percent = k_percent.rolling(window=d_period).mean()
        j_value = 3 * k_percent - 2 * d_percent
        
        return k_percent, d_percent, j_value
    
    def calculate_atr(self, period=14):
        """计算平均真实波幅(ATR)"""
        high = self.df['high']
        low = self.df['low']
        close = self.df['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        
        return atr
    
    def calculate_obv(self):
        """计算能量潮指标(OBV)"""
        obv = np.where(self.df['close'] > self.df['close'].shift(), 
                       self.df['volume'], 
                       np.where(self.df['close'] < self.df['close'].shift(), 
                               -self.df['volume'], 0))
        return pd.Series(obv, index=self.df.index).cumsum()
    
    def calculate_all_indicators(self):
        """计算所有指标"""
        print(f"\n计算 {self.symbol} {self.interval} 的技术指标...")
        
        # 创建结果DataFrame
        results = pd.DataFrame(index=self.df.index)
        
        # 添加原始数据
        results['open'] = self.df['open']
        results['high'] = self.df['high']
        results['low'] = self.df['low']
        results['close'] = self.df['close']
        results['volume'] = self.df['volume']
        
        # # 计算移动平均线
        # results['MA5'] = self.calculate_ma(5)
        # results['MA10'] = self.calculate_ma(10)
        # results['MA20'] = self.calculate_ma(20)
        # results['MA30'] = self.calculate_ma(30)
        
        # # 计算EMA
        # results['EMA12'] = self.calculate_ema(12)
        # results['EMA26'] = self.calculate_ema(26)
        
        # # 计算RSI
        # results['RSI14'] = self.calculate_rsi(14)
        
        # # 计算MACD
        # macd, signal, histogram = self.calculate_macd()
        # results['MACD'] = macd
        # results['MACD_Signal'] = signal
        # results['MACD_Histogram'] = histogram
        
        # 计算布林带
        upper, middle, lower, above_upper_pct, below_lower_pct = self.calculate_bollinger_bands()
        results['BB_Upper'] = upper
        results['BB_Middle'] = middle
        results['BB_Lower'] = lower
        results['BB_Above_Upper_Pct'] = above_upper_pct
        results['BB_Below_Lower_Pct'] = below_lower_pct
        
        # # 计算KDJ
        # k, d, j = self.calculate_stochastic()
        # results['Stoch_K'] = k
        # results['Stoch_D'] = d
        # results['Stoch_J'] = j
        
        # # 计算ATR
        # results['ATR14'] = self.calculate_atr(14)
        
        # # 计算OBV
        # results['OBV'] = self.calculate_obv()
        
        return results
    
    async def run(self):
        """运行指标计算并导出CSV"""
        # 获取K线数据
        df = await self.get_kline_data()
        if df is None:
            return
        
        print(f"获取到 {len(df)} 根K线数据")
        
        # 计算所有指标
        results = self.calculate_all_indicators()
        
        # 创建输出目录
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{output_dir}/{self.symbol}_{self.interval}_indicators_{timestamp}.csv"
        
        # 保存到CSV
        results.to_csv(filename, index=True, float_format='%.6f')
        print(f"\n指标数据已保存到: {filename}")
        
        # 显示最后几行数据
        print(f"\n最新5根K线的指标数据:")
        print(results.tail())
        
        # 显示统计信息
        print(f"\n指标统计信息:")
        print(results.describe())


async def main():
    """主函数 - 硬编码参数"""
    # 硬编码参数配置
    SYMBOL = 'BTCUSDT'      # 交易对
    INTERVAL = '15m'         # K线周期 (1m, 5m, 15m, 30m, 1h, 4h, 1d)
    BARS_COUNT = 1000        # 获取K线根数
    
    print(f"=== 技术指标计算器 ===")
    print(f"交易对: {SYMBOL}")
    print(f"K线周期: {INTERVAL}")
    print(f"数据根数: {BARS_COUNT}")
    
    # 创建计算器实例
    calculator = IndicatorCalculator(
        symbol=SYMBOL,
        interval=INTERVAL,
        bars_count=BARS_COUNT
    )
    
    # 运行计算
    await calculator.run()


if __name__ == "__main__":
    asyncio.run(main())
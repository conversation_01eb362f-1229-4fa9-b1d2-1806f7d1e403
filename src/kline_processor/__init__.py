"""
K-line processor package for real-time cryptocurrency data processing.

This package provides comprehensive functionality for fetching, processing,
storing, and aggregating cryptocurrency K-line (candlestick) data.
"""

from .main import KlineProcessor, app
from .config.settings import Settings

__version__ = "1.0.0"
__author__ = "K-line Processor Team"
__description__ = "Real-time cryptocurrency K-line data processor"

__all__ = [
    "KlineProcessor",
    "app", 
    "Settings",
    "__version__",
    "__author__",
    "__description__"
]


def main() -> None:
    """Main entry point for CLI."""
    from .cli import cli
    cli()

"""
WebSocket message models and data structures.

This module defines the data structures for WebSocket communication,
including message wrappers, ticker data, and stream formats.
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional, Union
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic.types import PositiveInt

from .kline import KlineData, ValidatedKlineData


class StreamType(Enum):
    """WebSocket stream types."""
    KLINE = "kline"
    TICKER = "ticker"
    DEPTH = "depth"
    TRADE = "trade"
    UNKNOWN = "unknown"


@dataclass
class WebSocketMessage:
    """
    WebSocket message wrapper for all incoming stream data.
    
    This dataclass represents the standard structure of WebSocket messages
    from exchanges, containing the stream identifier and parsed data.
    """
    stream: str                 # Stream name (e.g., "btcusdt@kline_1m")
    data: Dict[str, Any]       # Raw message data
    
    def get_symbol(self) -> Optional[str]:
        """Extract symbol from stream name."""
        if '@' in self.stream:
            return self.stream.split('@')[0].upper()
        return None
    
    def get_stream_type(self) -> StreamType:
        """Extract stream type from stream name."""
        if '@' in self.stream:
            stream_type_str = self.stream.split('@')[1]
            if stream_type_str.startswith('kline_'):
                return StreamType.KLINE
            elif stream_type_str == 'ticker':
                return StreamType.TICKER
            elif stream_type_str.startswith('depth'):
                return StreamType.DEPTH
            elif stream_type_str == 'trade':
                return StreamType.TRADE
        return StreamType.UNKNOWN
    
    def is_kline_stream(self) -> bool:
        """Check if this is a K-line stream message."""
        return self.get_stream_type() == StreamType.KLINE
    
    def is_ticker_stream(self) -> bool:
        """Check if this is a ticker stream message."""
        return self.get_stream_type() == StreamType.TICKER
    
    def to_kline_data(self) -> Optional[KlineData]:
        """Convert WebSocket message to KlineData if it's a K-line stream."""
        if not self.is_kline_stream():
            return None
        
        # The WebSocket message structure is: {stream: str, data: {e: kline, k: {...}}}
        # We need to check for 'k' in the nested 'data' object
        message_data = self.data.get('data', self.data)  # Handle both nested and flat structures
        if 'k' not in message_data:
            return None
        
        kline_raw = message_data['k']
        return KlineData(
            t=kline_raw['t'],
            T=kline_raw['T'],
            s=kline_raw['s'],
            i=kline_raw['i'],
            f=kline_raw['f'],
            L=kline_raw['L'],
            o=kline_raw['o'],
            c=kline_raw['c'],
            h=kline_raw['h'],
            l=kline_raw['l'],
            v=kline_raw['v'],
            n=kline_raw['n'],
            x=kline_raw['x'],
            q=kline_raw['q'],
            V=kline_raw['V'],
            Q=kline_raw['Q'],
            B=kline_raw.get('B', '0'),
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WebSocketMessage':
        """Create WebSocketMessage from dictionary data."""
        # Extract stream name from data or generate it
        stream = data.get('stream', 'unknown@unknown')
        
        # If no stream field, try to construct from event data
        if stream == 'unknown@unknown' and 's' in data:
            symbol = data['s'].lower()
            if 'e' in data:
                event_type = data['e']
                if event_type == 'kline':
                    # Extract interval from kline data
                    if 'k' in data and 'i' in data['k']:
                        interval = data['k']['i']
                        stream = f"{symbol}@kline_{interval}"
                    else:
                        stream = f"{symbol}@kline_1m"
                elif event_type == '24hrTicker':
                    stream = f"{symbol}@ticker"
                else:
                    stream = f"{symbol}@{event_type}"
        
        return cls(stream=stream, data=data)


class TickerData(BaseModel):
    """
    24hr ticker statistics data model with comprehensive validation.
    
    This model represents 24-hour ticker statistics from exchanges,
    including price changes, volumes, and trading statistics.
    """
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid'
    )
    
    # Event metadata
    e: str = Field(
        default="24hrTicker",
        description="Event type",
        examples=["24hrTicker"]
    )
    E: PositiveInt = Field(
        description="Event time (timestamp in milliseconds)",
        examples=[1640995200000, 1640995260000]
    )
    
    # Symbol
    s: str = Field(
        min_length=3,
        max_length=20,
        pattern=r'^[A-Z0-9]+$',
        description="Symbol (uppercase alphanumeric)",
        examples=["BTCUSDT", "ETHUSDT", "ADAUSDT"]
    )
    
    # Price change statistics
    p: str = Field(
        pattern=r'^-?\d+\.?\d*$',
        description="Price change (can be negative)",
        examples=["123.45", "-67.89", "0.00"]
    )
    P: str = Field(
        pattern=r'^-?\d+\.?\d*$',
        description="Price change percent (can be negative)",
        examples=["2.75", "-1.45", "0.00"]
    )
    
    # Weighted average price
    w: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Weighted average price",
        examples=["45123.456789", "3198.123456"]
    )
    
    # Previous day statistics
    x: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Previous day's close price",
        examples=["44876.11", "3265.012345"]
    )
    
    # Current day statistics
    c: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Current day's close price (last price)",
        examples=["45000.12", "3200.456789"]
    )
    Q: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Close quantity",
        examples=["0.12345678", "1.98765432"]
    )
    
    # Best bid/ask
    b: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Best bid price",
        examples=["44999.99", "3199.876543"]
    )
    B: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Best bid quantity",
        examples=["0.56789123", "2.34567890"]
    )
    a: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Best ask price",
        examples=["45000.01", "3200.123456"]
    )
    A: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Best ask quantity",
        examples=["0.98765432", "1.23456789"]
    )
    
    # Open price and time
    o: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Open price",
        examples=["44876.50", "3265.789012"]
    )
    h: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="High price",
        examples=["45200.00", "3300.567890"]
    )
    l: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Low price",
        examples=["44800.25", "3180.012345"]
    )
    
    # Volume statistics
    v: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Total traded base asset volume",
        examples=["12345.67890123", "987.654321098"]
    )
    q: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Total traded quote asset volume",
        examples=["567890123.45", "3141592.653589"]
    )
    
    # Time window
    O: PositiveInt = Field(
        description="Statistics open time (timestamp in milliseconds)",
        examples=[1640908800000, 1640908860000]
    )
    C: PositiveInt = Field(
        description="Statistics close time (timestamp in milliseconds)",
        examples=[1640995199999, 1640995259999]
    )
    
    # First and last trade info
    F: PositiveInt = Field(
        description="First trade ID",
        examples=[1234567890, 9876543210]
    )
    L: PositiveInt = Field(
        description="Last trade ID",
        examples=[1234567899, 9876543219]
    )
    
    # Trade count
    n: PositiveInt = Field(
        description="Total number of trades",
        examples=[15000, 27500, 5000]
    )
    
    @field_validator('C', mode='after')
    @classmethod
    def validate_close_time(cls, v: int, info) -> int:
        """Validate that close time is after open time."""
        if 'O' in info.data and v <= info.data['O']:
            raise ValueError('Close time must be after open time')
        return v
    
    @field_validator('L', mode='after')
    @classmethod
    def validate_trade_ids(cls, v: int, info) -> int:
        """Validate that last trade ID is >= first trade ID."""
        if 'F' in info.data and v < info.data['F']:
            raise ValueError('Last trade ID must be >= first trade ID')
        return v
    
    def get_decimal_prices(self) -> Dict[str, Decimal]:
        """Get price data as Decimal objects for precise calculations."""
        return {
            'open': Decimal(self.o),
            'high': Decimal(self.h),
            'low': Decimal(self.l),
            'close': Decimal(self.c),
            'weighted_avg': Decimal(self.w),
            'prev_close': Decimal(self.x),
            'price_change': Decimal(self.p),
            'price_change_percent': Decimal(self.P),
            'best_bid': Decimal(self.b),
            'best_ask': Decimal(self.a),
        }
    
    def get_decimal_volumes(self) -> Dict[str, Decimal]:
        """Get volume data as Decimal objects for precise calculations."""
        return {
            'base_volume': Decimal(self.v),
            'quote_volume': Decimal(self.q),
            'close_quantity': Decimal(self.Q),
            'best_bid_quantity': Decimal(self.B),
            'best_ask_quantity': Decimal(self.A),
        }
    
    def get_spread(self) -> Decimal:
        """Calculate bid-ask spread."""
        return Decimal(self.a) - Decimal(self.b)
    
    def get_spread_percentage(self) -> Decimal:
        """Calculate bid-ask spread as percentage of mid price."""
        spread = self.get_spread()
        mid_price = (Decimal(self.a) + Decimal(self.b)) / 2
        return (spread / mid_price) * 100 if mid_price > 0 else Decimal('0')


class StreamEventData(BaseModel):
    """
    Generic stream event data model for different stream types.
    
    This model handles various stream event types and provides
    unified access to stream data.
    """
    
    model_config = ConfigDict(
        extra='allow'  # Allow extra fields for different stream types
    )
    
    stream: str = Field(
        description="Stream name",
        examples=["btcusdt@kline_1m", "btcusdt@ticker", "btcusdt@depth"]
    )
    data: Dict[str, Any] = Field(
        description="Stream event data"
    )
    
    def get_symbol(self) -> Optional[str]:
        """Extract symbol from stream name."""
        if '@' in self.stream:
            return self.stream.split('@')[0].upper()
        return None
    
    def get_stream_type(self) -> Optional[str]:
        """Extract stream type from stream name."""
        if '@' in self.stream:
            return self.stream.split('@')[1]
        return None
    
    def is_kline_stream(self) -> bool:
        """Check if this is a K-line stream."""
        stream_type = self.get_stream_type()
        return stream_type is not None and stream_type.startswith('kline_')
    
    def is_ticker_stream(self) -> bool:
        """Check if this is a ticker stream."""
        return self.get_stream_type() == 'ticker'
    
    def to_kline_data(self) -> Optional[ValidatedKlineData]:
        """Convert to validated KlineData if applicable."""
        if not self.is_kline_stream() or 'k' not in self.data:
            return None
        
        try:
            kline_raw = self.data['k']
            return ValidatedKlineData(
                t=kline_raw['t'],
                T=kline_raw['T'],
                s=kline_raw['s'],
                i=kline_raw['i'],
                f=kline_raw['f'],
                L=kline_raw['L'],
                o=kline_raw['o'],
                c=kline_raw['c'],
                h=kline_raw['h'],
                l=kline_raw['l'],
                v=kline_raw['v'],
                n=kline_raw['n'],
                x=kline_raw['x'],
                q=kline_raw['q'],
                V=kline_raw['V'],
                Q=kline_raw['Q'],
                B=kline_raw.get('B', '0')
            )
        except Exception:
            return None
    
    def to_ticker_data(self) -> Optional[TickerData]:
        """Convert to TickerData if applicable."""
        if not self.is_ticker_stream():
            return None
        
        try:
            return TickerData(**self.data)
        except Exception:
            return None
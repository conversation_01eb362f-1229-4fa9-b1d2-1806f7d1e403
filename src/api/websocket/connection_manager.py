"""
WebSocket connection management with subscription handling.
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from fastapi import WebSocket
import uuid
import asyncio
from collections import defaultdict
import json

from kline_processor.utils.logger import get_logger

logger = get_logger("websocket.manager")


class ConnectionManager:
    """Manages WebSocket connections and their subscriptions."""
    
    def __init__(self):
        # Connection ID -> WebSocket mapping
        self._connections: Dict[str, WebSocket] = {}
        
        # Connection ID -> Set of (symbol, interval) subscriptions
        self._subscriptions: Dict[str, Set[Tuple[str, str]]] = defaultdict(set)
        
        # (symbol, interval) -> Set of connection IDs
        self._subscribers: Dict[Tuple[str, str], Set[str]] = defaultdict(set)
        
        # Lock for thread-safe operations
        self._lock = asyncio.Lock()
    
    async def connect(self, websocket: WebSocket) -> str:
        """Accept a new WebSocket connection."""
        await websocket.accept()
        connection_id = str(uuid.uuid4())
        
        async with self._lock:
            self._connections[connection_id] = websocket
            self._subscriptions[connection_id] = set()
            
        logger.info(f"WebSocket connected: {connection_id}")
        return connection_id
    
    async def disconnect(self, connection_id: str):
        """Remove a WebSocket connection and its subscriptions."""
        async with self._lock:
            # Remove all subscriptions
            if connection_id in self._subscriptions:
                for symbol, interval in self._subscriptions[connection_id]:
                    self._subscribers[(symbol, interval)].discard(connection_id)
                del self._subscriptions[connection_id]
            
            # Remove connection
            if connection_id in self._connections:
                del self._connections[connection_id]
                
        logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def subscribe(self, connection_id: str, symbol: str, interval: str):
        """Subscribe a connection to K-line updates."""
        async with self._lock:
            if connection_id in self._connections:
                key = (symbol, interval)
                self._subscriptions[connection_id].add(key)
                self._subscribers[key].add(connection_id)
                logger.info(f"Subscribed {connection_id} to {symbol}@{interval}")
                return True
        return False
    
    async def unsubscribe(self, connection_id: str, symbol: str, interval: str):
        """Unsubscribe a connection from K-line updates."""
        async with self._lock:
            if connection_id in self._subscriptions:
                key = (symbol, interval)
                self._subscriptions[connection_id].discard(key)
                self._subscribers[key].discard(connection_id)
                logger.info(f"Unsubscribed {connection_id} from {symbol}@{interval}")
                return True
        return False
    
    def get_subscriptions(self, connection_id: str) -> Set[Tuple[str, str]]:
        """Get all subscriptions for a connection."""
        return self._subscriptions.get(connection_id, set()).copy()
    
    async def send_json(self, connection_id: str, data: dict):
        """Send JSON data to a specific connection."""
        if connection_id in self._connections:
            try:
                await self._connections[connection_id].send_json(data)
            except Exception as e:
                logger.error(f"Error sending to {connection_id}: {e}")
                await self.disconnect(connection_id)
    
    async def send_text(self, connection_id: str, data: str):
        """Send text data to a specific connection."""
        if connection_id in self._connections:
            try:
                await self._connections[connection_id].send_text(data)
            except Exception as e:
                logger.error(f"Error sending to {connection_id}: {e}")
                await self.disconnect(connection_id)
    
    async def send_error(self, connection_id: str, message: str):
        """Send error message to a specific connection."""
        await self.send_json(connection_id, {
            "type": "error",
            "message": message
        })
    
    async def broadcast_kline(self, symbol: str, interval: str, kline_data: dict):
        """Broadcast K-line update to all subscribers."""
        key = (symbol, interval)
        subscribers = self._subscribers.get(key, set()).copy()
        
        if subscribers:
            logger.debug(f"Broadcasting {symbol}@{interval} to {len(subscribers)} clients")
            
            # Send to all subscribers concurrently
            tasks = []
            for connection_id in subscribers:
                task = self.send_json(connection_id, {
                    "type": "kline",
                    "data": kline_data
                })
                tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
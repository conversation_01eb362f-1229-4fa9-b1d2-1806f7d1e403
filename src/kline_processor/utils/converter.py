"""
Data conversion utilities for K-line data processing.

This module provides utilities for converting between different data formats,
handling API responses, and performing data transformations.
"""

from typing import Dict, List, Any, Optional, Union
from decimal import Decimal
from datetime import datetime, timezone

from ..models.kline import KlineData, ValidatedKlineData
from ..models.websocket import TickerData
from .timeframe import TimeframeUtils


class DataConverter:
    """Utility class for data conversion and transformation operations."""
    
    @staticmethod
    def api_response_to_kline_data(api_data: List[Any]) -> Optional[KlineData]:
        """
        Convert API response array to KlineData object.
        
        API response format from Binance:
        [
            1499040000000,      // Open time
            "0.01634790",       // Open
            "0.80000000",       // High  
            "0.01575800",       // Low
            "0.01577100",       // Close
            "148976.11427815",  // Volume
            1499644799999,      // Close time
            "2434.19055334",    // Quote asset volume
            308,                // Number of trades
            "1756.87402397",    // Taker buy base asset volume
            "28.46694368",      // Taker buy quote asset volume
            "17928899.62484339" // Ignore (unused field)
        ]
        
        Args:
            api_data: List containing K-line data from API response
            
        Returns:
            KlineData object or None if conversion fails
            
        Examples:
            >>> api_data = [1640995200000, "45000.12", "45200.00", ...]
            >>> kline = DataConverter.api_response_to_kline_data(api_data)
            >>> kline.t
            1640995200000
        """
        if not api_data or len(api_data) < 11:
            return None
        
        try:
            # Extract and validate required fields
            open_time = int(api_data[0])
            close_time = int(api_data[6])
            num_trades = int(api_data[8])
            
            # Convert string prices and volumes
            open_price = str(api_data[1])
            high_price = str(api_data[2])
            low_price = str(api_data[3])
            close_price = str(api_data[4])
            volume = str(api_data[5])
            quote_volume = str(api_data[7])
            taker_buy_volume = str(api_data[9])
            taker_buy_quote_volume = str(api_data[10])
            
            return KlineData(
                t=open_time,
                T=close_time,
                s="",  # Symbol will be set by caller
                i="",  # Interval will be set by caller
                f=0,   # First trade ID - not available in API response
                L=0,   # Last trade ID - not available in API response
                o=open_price,
                c=close_price,
                h=high_price,
                l=low_price,
                v=volume,
                n=num_trades,
                x=True,  # Assume closed - API historical data is always closed
                q=quote_volume,
                V=taker_buy_volume,
                Q=taker_buy_quote_volume,
                B="0"   # Unused field
            )
            
        except (ValueError, IndexError, TypeError):
            return None
    
    @staticmethod
    def kline_data_to_api_format(kline: KlineData) -> List[Any]:
        """
        Convert KlineData object to API response format.
        
        Args:
            kline: KlineData object to convert
            
        Returns:
            List in API response format
            
        Examples:
            >>> kline = KlineData(t=1640995200000, o="45000.12", ...)
            >>> api_format = DataConverter.kline_data_to_api_format(kline)
            >>> api_format[0]
            1640995200000
        """
        return [
            kline.t,        # Open time
            kline.o,        # Open
            kline.h,        # High
            kline.l,        # Low
            kline.c,        # Close
            kline.v,        # Volume
            kline.T,        # Close time
            kline.q,        # Quote asset volume
            kline.n,        # Number of trades
            kline.V,        # Taker buy base asset volume
            kline.Q,        # Taker buy quote asset volume
            kline.B         # Unused field
        ]
    
    @staticmethod
    def websocket_to_kline_data(ws_data: Dict[str, Any], symbol: str, interval: str) -> Optional[KlineData]:
        """
        Convert WebSocket K-line data to KlineData object.
        
        Args:
            ws_data: WebSocket K-line data dictionary
            symbol: Trading symbol (e.g., "BTCUSDT")
            interval: Timeframe interval (e.g., "1m")
            
        Returns:
            KlineData object or None if conversion fails
            
        Examples:
            >>> ws_data = {'t': 1640995200000, 'o': '45000.12', ...}
            >>> kline = DataConverter.websocket_to_kline_data(ws_data, "BTCUSDT", "1m")
            >>> kline.s
            "BTCUSDT"
        """
        if not ws_data:
            return None
        
        try:
            return KlineData(
                t=int(ws_data['t']),
                T=int(ws_data['T']),
                s=symbol,
                i=interval,
                f=int(ws_data['f']),
                L=int(ws_data['L']),
                o=str(ws_data['o']),
                c=str(ws_data['c']),
                h=str(ws_data['h']),
                l=str(ws_data['l']),
                v=str(ws_data['v']),
                n=int(ws_data['n']),
                x=bool(ws_data['x']),
                q=str(ws_data['q']),
                V=str(ws_data['V']),
                Q=str(ws_data['Q']),
                B=str(ws_data.get('B', '0'))
            )
            
        except (KeyError, ValueError, TypeError):
            return None
    
    @staticmethod
    def aggregate_klines(klines: List[KlineData], target_timeframe: str) -> Optional[KlineData]:
        """
        Aggregate multiple K-line data points into a single timeframe.
        
        Args:
            klines: List of KlineData objects to aggregate
            target_timeframe: Target timeframe for aggregation
            
        Returns:
            Aggregated KlineData object or None if aggregation fails
            
        Examples:
            >>> klines = [kline1, kline2, kline3]  # 1m K-lines
            >>> aggregated = DataConverter.aggregate_klines(klines, "5m")
            >>> aggregated.i
            "5m"
        """
        if not klines:
            return None
        
        # Sort by open time
        sorted_klines = sorted(klines, key=lambda k: k.t)
        first_kline = sorted_klines[0]
        last_kline = sorted_klines[-1]
        
        try:
            # Calculate OHLC
            open_price = first_kline.o
            close_price = last_kline.c
            
            # Find highest high and lowest low
            high_prices = [Decimal(k.h) for k in sorted_klines]
            low_prices = [Decimal(k.l) for k in sorted_klines]
            high_price = str(max(high_prices))
            low_price = str(min(low_prices))
            
            # Sum volumes
            total_volume = sum(Decimal(k.v) for k in sorted_klines)
            total_quote_volume = sum(Decimal(k.q) for k in sorted_klines)
            total_taker_buy_volume = sum(Decimal(k.V) for k in sorted_klines)
            total_taker_buy_quote_volume = sum(Decimal(k.Q) for k in sorted_klines)
            
            # Sum trade counts
            total_trades = sum(k.n for k in sorted_klines)
            
            # Determine if aggregated period is closed
            # All constituent klines should be closed for aggregated kline to be closed
            is_closed = all(k.x for k in sorted_klines)
            
            # Use first and last trade IDs
            first_trade_id = first_kline.f
            last_trade_id = last_kline.L
            
            return KlineData(
                t=first_kline.t,
                T=last_kline.T,
                s=first_kline.s,
                i=target_timeframe,
                f=first_trade_id,
                L=last_trade_id,
                o=open_price,
                c=close_price,
                h=high_price,
                l=low_price,
                v=str(total_volume),
                n=total_trades,
                x=is_closed,
                q=str(total_quote_volume),
                V=str(total_taker_buy_volume),
                Q=str(total_taker_buy_quote_volume),
                B="0"  # Unused field
            )
            
        except (ValueError, TypeError, AttributeError):
            return None
    
    @staticmethod
    def update_kline_incremental(base_kline: KlineData, update_data: Dict[str, Any]) -> Optional[KlineData]:
        """
        Update K-line data with incremental changes.
        
        Args:
            base_kline: Base KlineData object to update
            update_data: Dictionary containing update fields
            
        Returns:
            Updated KlineData object or None if update fails
            
        Examples:
            >>> base = KlineData(...)
            >>> updates = {'c': '45150.00', 'h': '45200.00', 'v': '125.5'}
            >>> updated = DataConverter.update_kline_incremental(base, updates)
            >>> updated.c
            "45150.00"
        """
        if not base_kline or not update_data:
            return None
        
        try:
            # Create dictionary from base kline
            kline_dict = base_kline.to_dict()
            
            # Apply updates
            for field, value in update_data.items():
                if field in kline_dict:
                    # Type conversion based on field
                    if field in ['t', 'T', 'f', 'L', 'n']:
                        kline_dict[field] = int(value)
                    elif field == 'x':
                        kline_dict[field] = bool(value)
                    else:
                        kline_dict[field] = str(value)
            
            # Create new KlineData from updated dictionary
            return KlineData.from_dict(kline_dict)
            
        except (ValueError, TypeError, AttributeError):
            return None
    
    @staticmethod
    def normalize_price_precision(price: str, precision: int = 8) -> str:
        """
        Normalize price string to specified decimal precision.
        
        Args:
            price: Price string to normalize
            precision: Number of decimal places
            
        Returns:
            Normalized price string
            
        Examples:
            >>> DataConverter.normalize_price_precision("45000.123456789", 2)
            "45000.12"
            >>> DataConverter.normalize_price_precision("45000", 2)
            "45000.00"
        """
        try:
            decimal_price = Decimal(price)
            # Round to specified precision
            rounded = decimal_price.quantize(Decimal('0.' + '0' * precision))
            return str(rounded)
        except (ValueError, TypeError, Exception):
            return price
    
    @staticmethod
    def normalize_volume_precision(volume: str, precision: int = 8) -> str:
        """
        Normalize volume string to specified decimal precision.
        
        Args:
            volume: Volume string to normalize
            precision: Number of decimal places
            
        Returns:
            Normalized volume string
            
        Examples:
            >>> DataConverter.normalize_volume_precision("123.456789012", 6)
            "123.456789"
        """
        try:
            decimal_volume = Decimal(volume)
            # Round to specified precision
            rounded = decimal_volume.quantize(Decimal('0.' + '0' * precision))
            return str(rounded)
        except (ValueError, TypeError, Exception):
            return volume
    
    @staticmethod
    def validate_kline_consistency(kline: KlineData) -> bool:
        """
        Validate K-line data for consistency and logical correctness.
        
        Args:
            kline: KlineData object to validate
            
        Returns:
            True if data is consistent, False otherwise
            
        Examples:
            >>> kline = KlineData(...)
            >>> DataConverter.validate_kline_consistency(kline)
            True
        """
        try:
            # Basic timestamp validation
            if kline.t >= kline.T:
                return False
            
            # OHLC validation
            if not kline.is_valid_ohlc():
                return False
            
            # Volume validation (non-negative)
            volumes = [kline.v, kline.q, kline.V, kline.Q]
            for vol in volumes:
                if Decimal(vol) < 0:
                    return False
            
            # Trade count validation
            if kline.n < 0:
                return False
            
            # Trade ID validation
            if kline.f > kline.L and kline.L > 0:
                return False
            
            return True
            
        except (ValueError, TypeError, AttributeError):
            return False
    
    @staticmethod
    def convert_to_validated_kline(kline: KlineData) -> Optional[ValidatedKlineData]:
        """
        Convert KlineData to ValidatedKlineData with validation.
        
        Args:
            kline: KlineData object to convert and validate
            
        Returns:
            ValidatedKlineData object or None if validation fails
            
        Examples:
            >>> kline = KlineData(...)
            >>> validated = DataConverter.convert_to_validated_kline(kline)
            >>> isinstance(validated, ValidatedKlineData)
            True
        """
        if not DataConverter.validate_kline_consistency(kline):
            return None
        
        try:
            return ValidatedKlineData.from_kline_data(kline)
        except Exception:
            return None
    
    @staticmethod
    def format_for_storage(kline: KlineData) -> Dict[str, Any]:
        """
        Format K-line data for Redis storage with optimized field names.
        
        Args:
            kline: KlineData object to format
            
        Returns:
            Dictionary optimized for Redis storage
            
        Examples:
            >>> kline = KlineData(...)
            >>> storage_dict = DataConverter.format_for_storage(kline)
            >>> 'ot' in storage_dict  # open_time
            True
        """
        return {
            'ot': kline.t,           # open_time
            'ct': kline.T,           # close_time
            's': kline.s,            # symbol
            'i': kline.i,            # interval
            'ft': kline.f,           # first_trade_id
            'lt': kline.L,           # last_trade_id
            'o': kline.o,            # open
            'c': kline.c,            # close
            'h': kline.h,            # high
            'l': kline.l,            # low
            'v': kline.v,            # volume
            'n': kline.n,            # num_trades
            'x': kline.x,            # is_closed
            'q': kline.q,            # quote_volume
            'bv': kline.V,           # taker_buy_volume
            'bq': kline.Q,           # taker_buy_quote_volume
        }
    
    @staticmethod
    def parse_from_storage(storage_data: Dict[str, Any]) -> Optional[KlineData]:
        """
        Parse K-line data from Redis storage format.
        
        Args:
            storage_data: Dictionary from Redis storage
            
        Returns:
            KlineData object or None if parsing fails
            
        Examples:
            >>> storage_dict = {'ot': 1640995200000, 'o': '45000.12', ...}
            >>> kline = DataConverter.parse_from_storage(storage_dict)
            >>> kline.t
            1640995200000
        """
        try:
            return KlineData(
                t=int(storage_data['ot']),
                T=int(storage_data['ct']),
                s=str(storage_data['s']),
                i=str(storage_data['i']),
                f=int(storage_data['ft']),
                L=int(storage_data['lt']),
                o=str(storage_data['o']),
                c=str(storage_data['c']),
                h=str(storage_data['h']),
                l=str(storage_data['l']),
                v=str(storage_data['v']),
                n=int(storage_data['n']),
                x=bool(storage_data['x']),
                q=str(storage_data['q']),
                V=str(storage_data['bv']),
                Q=str(storage_data['bq']),
                B="0"  # Default unused field
            )
        except (KeyError, ValueError, TypeError):
            return None
    
    @staticmethod
    def calculate_price_change(old_price: str, new_price: str) -> Dict[str, str]:
        """
        Calculate price change and percentage change.
        
        Args:
            old_price: Previous price as string
            new_price: New price as string
            
        Returns:
            Dictionary with absolute and percentage changes
            
        Examples:
            >>> changes = DataConverter.calculate_price_change("45000.00", "45123.45")
            >>> changes['absolute']
            "123.45"
            >>> changes['percentage']
            "0.274333"
        """
        try:
            old_decimal = Decimal(old_price)
            new_decimal = Decimal(new_price)
            
            absolute_change = new_decimal - old_decimal
            percentage_change = (absolute_change / old_decimal * 100) if old_decimal != 0 else Decimal('0')
            
            # Format absolute change to remove unnecessary trailing zeros
            absolute_str = str(absolute_change)
            if '.' in absolute_str:
                absolute_str = absolute_str.rstrip('0').rstrip('.')
            
            return {
                'absolute': absolute_str,
                'percentage': str(percentage_change.quantize(Decimal('0.000001')))
            }
        except (ValueError, TypeError, Exception):
            return {
                'absolute': '0',
                'percentage': '0'
            }
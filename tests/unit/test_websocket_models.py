"""
Unit tests for WebSocket message models.
"""

import pytest
from decimal import Decimal
from pydantic import ValidationError

from kline_processor.models.websocket import WebSocketMessage, TickerData, StreamEventData
from kline_processor.models.kline import KlineData, ValidatedKlineData


class TestWebSocketMessage:
    """Test cases for WebSocketMessage dataclass."""
    
    def test_websocket_message_creation(self):
        """Test basic WebSocketMessage creation."""
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={'k': {'t': 1640995200000, 's': 'BTCUSDT'}}
        )
        
        assert message.stream == "btcusdt@kline_1m"
        assert message.data == {'k': {'t': 1640995200000, 's': 'BTCUSDT'}}
    
    def test_get_symbol(self):
        """Test symbol extraction from stream name."""
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={}
        )
        
        assert message.get_symbol() == "BTCUSDT"
    
    def test_get_symbol_no_at_symbol(self):
        """Test symbol extraction when no @ in stream name."""
        message = WebSocketMessage(
            stream="invalid_stream",
            data={}
        )
        
        assert message.get_symbol() is None
    
    def test_get_stream_type(self):
        """Test stream type extraction."""
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={}
        )
        
        assert message.get_stream_type() == "kline_1m"
    
    def test_get_stream_type_ticker(self):
        """Test ticker stream type extraction."""
        message = WebSocketMessage(
            stream="btcusdt@ticker",
            data={}
        )
        
        assert message.get_stream_type() == "ticker"
    
    def test_is_kline_stream_true(self):
        """Test K-line stream detection."""
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={}
        )
        
        assert message.is_kline_stream() is True
    
    def test_is_kline_stream_false(self):
        """Test non-K-line stream detection."""
        message = WebSocketMessage(
            stream="btcusdt@ticker",
            data={}
        )
        
        assert message.is_kline_stream() is False
    
    def test_is_ticker_stream_true(self):
        """Test ticker stream detection."""
        message = WebSocketMessage(
            stream="btcusdt@ticker",
            data={}
        )
        
        assert message.is_ticker_stream() is True
    
    def test_is_ticker_stream_false(self):
        """Test non-ticker stream detection."""
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={}
        )
        
        assert message.is_ticker_stream() is False
    
    def test_to_kline_data_success(self):
        """Test successful conversion to KlineData."""
        kline_raw = {
            't': 1640995200000,
            'T': 1640995259999,
            's': 'BTCUSDT',
            'i': '1m',
            'f': 1234567890,
            'L': 1234567899,
            'o': '45000.12',
            'c': '45123.45',
            'h': '45200.00',
            'l': '44950.50',
            'v': '123.456789',
            'n': 1500,
            'x': True,
            'q': '5567890.12',
            'V': '62.123456',
            'Q': '2784567.89',
            'B': '0'
        }
        
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={'k': kline_raw}
        )
        
        kline_data = message.to_kline_data()
        assert isinstance(kline_data, KlineData)
        assert kline_data.t == 1640995200000
        assert kline_data.s == 'BTCUSDT'
        assert kline_data.x is True
    
    def test_to_kline_data_not_kline_stream(self):
        """Test conversion fails for non-K-line stream."""
        message = WebSocketMessage(
            stream="btcusdt@ticker",
            data={'e': '24hrTicker'}
        )
        
        kline_data = message.to_kline_data()
        assert kline_data is None
    
    def test_to_kline_data_missing_k_field(self):
        """Test conversion fails when k field is missing."""
        message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={'other': 'data'}
        )
        
        kline_data = message.to_kline_data()
        assert kline_data is None


class TestTickerData:
    """Test cases for TickerData Pydantic model."""
    
    def test_valid_ticker_data_creation(self):
        """Test creation of valid TickerData."""
        ticker = TickerData(
            E=1640995200000,
            s="BTCUSDT",
            p="123.45",
            P="2.75",
            w="45123.456789",
            x="44876.11",
            c="45000.12",
            Q="0.12345678",
            b="44999.99",
            B="0.56789123",
            a="45000.01",
            A="0.98765432",
            o="44876.50",
            h="45200.00",
            l="44800.25",
            v="12345.67890123",
            q="567890123.45",
            O=1640908800000,
            C=1640995199999,
            F=1234567890,
            L=1234567899,
            n=15000
        )
        
        assert ticker.s == "BTCUSDT"
        assert ticker.E == 1640995200000
        assert ticker.n == 15000
    
    def test_invalid_symbol(self):
        """Test validation error for invalid symbol."""
        with pytest.raises(ValidationError) as exc_info:
            TickerData(
                E=1640995200000,
                s="btcusdt",  # lowercase not allowed
                p="123.45",
                P="2.75",
                w="45123.456789",
                x="44876.11",
                c="45000.12",
                Q="0.12345678",
                b="44999.99",
                B="0.56789123",
                a="45000.01",
                A="0.98765432",
                o="44876.50",
                h="45200.00",
                l="44800.25",
                v="12345.67890123",
                q="567890123.45",
                O=1640908800000,
                C=1640995199999,
                F=1234567890,
                L=1234567899,
                n=15000
            )
        
        assert "string_pattern_mismatch" in str(exc_info.value)
    
    def test_negative_price_change(self):
        """Test handling of negative price change."""
        ticker = TickerData(
            E=1640995200000,
            s="BTCUSDT",
            p="-123.45",  # negative price change
            P="-2.75",   # negative percentage change
            w="45123.456789",
            x="44876.11",
            c="45000.12",
            Q="0.12345678",
            b="44999.99",
            B="0.56789123",
            a="45000.01",
            A="0.98765432",
            o="44876.50",
            h="45200.00",
            l="44800.25",
            v="12345.67890123",
            q="567890123.45",
            O=1640908800000,
            C=1640995199999,
            F=1234567890,
            L=1234567899,
            n=15000
        )
        
        assert ticker.p == "-123.45"
        assert ticker.P == "-2.75"
    
    def test_close_time_validation(self):
        """Test validation that close time is after open time."""
        with pytest.raises(ValidationError) as exc_info:
            TickerData(
                E=1640995200000,
                s="BTCUSDT",
                p="123.45",
                P="2.75",
                w="45123.456789",
                x="44876.11",
                c="45000.12",
                Q="0.12345678",
                b="44999.99",
                B="0.56789123",
                a="45000.01",
                A="0.98765432",
                o="44876.50",
                h="45200.00",
                l="44800.25",
                v="12345.67890123",
                q="567890123.45",
                O=1640995199999,  # open time
                C=1640908800000,  # close time before open time
                F=1234567890,
                L=1234567899,
                n=15000
            )
        
        assert "Close time must be after open time" in str(exc_info.value)
    
    def test_trade_id_validation(self):
        """Test validation that last trade ID >= first trade ID."""
        with pytest.raises(ValidationError) as exc_info:
            TickerData(
                E=1640995200000,
                s="BTCUSDT",
                p="123.45",
                P="2.75",
                w="45123.456789",
                x="44876.11",
                c="45000.12",
                Q="0.12345678",
                b="44999.99",
                B="0.56789123",
                a="45000.01",
                A="0.98765432",
                o="44876.50",
                h="45200.00",
                l="44800.25",
                v="12345.67890123",
                q="567890123.45",
                O=1640908800000,
                C=1640995199999,
                F=1234567899,  # first trade ID
                L=1234567890,  # last trade ID less than first
                n=15000
            )
        
        assert "Last trade ID must be >= first trade ID" in str(exc_info.value)
    
    def test_get_decimal_prices(self):
        """Test getting prices as Decimal objects."""
        ticker = TickerData(
            E=1640995200000,
            s="BTCUSDT",
            p="123.45",
            P="2.75",
            w="45123.456789",
            x="44876.11",
            c="45000.12",
            Q="0.12345678",
            b="44999.99",
            B="0.56789123",
            a="45000.01",
            A="0.98765432",
            o="44876.50",
            h="45200.00",
            l="44800.25",
            v="12345.67890123",
            q="567890123.45",
            O=1640908800000,
            C=1640995199999,
            F=1234567890,
            L=1234567899,
            n=15000
        )
        
        prices = ticker.get_decimal_prices()
        assert prices['open'] == Decimal("44876.50")
        assert prices['close'] == Decimal("45000.12")
        assert prices['price_change'] == Decimal("123.45")
        assert prices['best_bid'] == Decimal("44999.99")
        assert prices['best_ask'] == Decimal("45000.01")
    
    def test_get_decimal_volumes(self):
        """Test getting volumes as Decimal objects."""
        ticker = TickerData(
            E=1640995200000,
            s="BTCUSDT",
            p="123.45",
            P="2.75",
            w="45123.456789",
            x="44876.11",
            c="45000.12",
            Q="0.12345678",
            b="44999.99",
            B="0.56789123",
            a="45000.01",
            A="0.98765432",
            o="44876.50",
            h="45200.00",
            l="44800.25",
            v="12345.67890123",
            q="567890123.45",
            O=1640908800000,
            C=1640995199999,
            F=1234567890,
            L=1234567899,
            n=15000
        )
        
        volumes = ticker.get_decimal_volumes()
        assert volumes['base_volume'] == Decimal("12345.67890123")
        assert volumes['quote_volume'] == Decimal("567890123.45")
        assert volumes['close_quantity'] == Decimal("0.12345678")
    
    def test_get_spread(self):
        """Test bid-ask spread calculation."""
        ticker = TickerData(
            E=1640995200000,
            s="BTCUSDT",
            p="123.45",
            P="2.75",
            w="45123.456789",
            x="44876.11",
            c="45000.12",
            Q="0.12345678",
            b="44999.99",  # bid
            B="0.56789123",
            a="45000.01",  # ask
            A="0.98765432",
            o="44876.50",
            h="45200.00",
            l="44800.25",
            v="12345.67890123",
            q="567890123.45",
            O=1640908800000,
            C=1640995199999,
            F=1234567890,
            L=1234567899,
            n=15000
        )
        
        spread = ticker.get_spread()
        expected_spread = Decimal("45000.01") - Decimal("44999.99")
        assert spread == expected_spread
    
    def test_get_spread_percentage(self):
        """Test bid-ask spread percentage calculation."""
        ticker = TickerData(
            E=1640995200000,
            s="BTCUSDT",
            p="123.45",
            P="2.75",
            w="45123.456789",
            x="44876.11",
            c="45000.12",
            Q="0.12345678",
            b="45000.00",  # bid
            B="0.56789123",
            a="45000.10",  # ask (spread of 0.10)
            A="0.98765432",
            o="44876.50",
            h="45200.00",
            l="44800.25",
            v="12345.67890123",
            q="567890123.45",
            O=1640908800000,
            C=1640995199999,
            F=1234567890,
            L=1234567899,
            n=15000
        )
        
        spread_pct = ticker.get_spread_percentage()
        # Spread = 0.10, Mid = 45000.05, Percentage = (0.10 / 45000.05) * 100 ≈ 0.000222%
        assert spread_pct > Decimal('0')
        assert spread_pct < Decimal('1')  # Should be a small percentage


class TestStreamEventData:
    """Test cases for StreamEventData Pydantic model."""
    
    def test_stream_event_data_creation(self):
        """Test basic StreamEventData creation."""
        event = StreamEventData(
            stream="btcusdt@kline_1m",
            data={'k': {'t': 1640995200000, 's': 'BTCUSDT'}}
        )
        
        assert event.stream == "btcusdt@kline_1m"
        assert event.data == {'k': {'t': 1640995200000, 's': 'BTCUSDT'}}
    
    def test_get_symbol(self):
        """Test symbol extraction."""
        event = StreamEventData(
            stream="ethusdt@ticker",
            data={}
        )
        
        assert event.get_symbol() == "ETHUSDT"
    
    def test_is_kline_stream(self):
        """Test K-line stream detection."""
        event = StreamEventData(
            stream="btcusdt@kline_5m",
            data={}
        )
        
        assert event.is_kline_stream() is True
    
    def test_is_ticker_stream(self):
        """Test ticker stream detection."""
        event = StreamEventData(
            stream="btcusdt@ticker",
            data={}
        )
        
        assert event.is_ticker_stream() is True
    
    def test_to_kline_data_success(self):
        """Test successful conversion to ValidatedKlineData."""
        kline_raw = {
            't': 1640995200000,
            'T': 1640995259999,
            's': 'BTCUSDT',
            'i': '1m',
            'f': 1234567890,
            'L': 1234567899,
            'o': '45000.12',
            'c': '45123.45',
            'h': '45200.00',
            'l': '44950.50',
            'v': '123.456789',
            'n': 1500,
            'x': True,
            'q': '5567890.12',
            'V': '62.123456',
            'Q': '2784567.89',
            'B': '0'
        }
        
        event = StreamEventData(
            stream="btcusdt@kline_1m",
            data={'k': kline_raw}
        )
        
        kline_data = event.to_kline_data()
        assert isinstance(kline_data, ValidatedKlineData)
        assert kline_data.t == 1640995200000
        assert kline_data.s == 'BTCUSDT'
    
    def test_to_kline_data_invalid_data(self):
        """Test conversion fails with invalid K-line data."""
        event = StreamEventData(
            stream="btcusdt@kline_1m",
            data={'k': {'invalid': 'data'}}  # Missing required fields
        )
        
        kline_data = event.to_kline_data()
        assert kline_data is None
    
    def test_to_ticker_data_success(self):
        """Test successful conversion to TickerData."""
        ticker_raw = {
            'e': '24hrTicker',
            'E': 1640995200000,
            's': 'BTCUSDT',
            'p': '123.45',
            'P': '2.75',
            'w': '45123.456789',
            'x': '44876.11',
            'c': '45000.12',
            'Q': '0.12345678',
            'b': '44999.99',
            'B': '0.56789123',
            'a': '45000.01',
            'A': '0.98765432',
            'o': '44876.50',
            'h': '45200.00',
            'l': '44800.25',
            'v': '12345.67890123',
            'q': '567890123.45',
            'O': 1640908800000,
            'C': 1640995199999,
            'F': 1234567890,
            'L': 1234567899,
            'n': 15000
        }
        
        event = StreamEventData(
            stream="btcusdt@ticker",
            data=ticker_raw
        )
        
        ticker_data = event.to_ticker_data()
        assert isinstance(ticker_data, TickerData)
        assert ticker_data.s == 'BTCUSDT'
        assert ticker_data.E == 1640995200000
    
    def test_to_ticker_data_not_ticker_stream(self):
        """Test conversion fails for non-ticker stream."""
        event = StreamEventData(
            stream="btcusdt@kline_1m",
            data={'k': {}}
        )
        
        ticker_data = event.to_ticker_data()
        assert ticker_data is None
#!/bin/bash

echo "Starting K-line processor..."
STARTUP_BACKFILL_ENABLED=false python -m src.kline_processor &
PID=$!

echo "Process started with PID: $PID"
echo "Waiting 5 seconds..."
sleep 5

echo "Sending interrupt signal (Ctrl+C)..."
kill -INT $PID

echo "Waiting for graceful shutdown..."
sleep 3

if ps -p $PID > /dev/null; then
    echo "Process still running, sending TERM signal..."
    kill -TERM $PID
    sleep 2
fi

if ps -p $PID > /dev/null; then
    echo "Process still running, force killing..."
    kill -9 $PID
else
    echo "Process terminated successfully!"
fi
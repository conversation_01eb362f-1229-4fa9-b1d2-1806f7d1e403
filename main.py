#!/usr/bin/env python3
"""
Main entry point for K-line processor with FastAPI server.

This script provides a unified interface to run the K-line processor
either as a standalone service or with the FastAPI web server.
"""

import click
import asyncio
import uvicorn
from pathlib import Path
import sys

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from kline_processor.config.settings import Settings
from kline_processor.__main__ import main as run_processor


@click.group()
def cli():
    """K-Line Processor - Cryptocurrency data processing system with API"""
    pass


@cli.command()
@click.option('--host', default='0.0.0.0', help='API server host')
@click.option('--port', default=8088, help='API server port')
@click.option('--reload', is_flag=True, help='Enable auto-reload for development')
@click.option('--workers', default=1, help='Number of worker processes')
def serve(host: str, port: int, reload: bool, workers: int):
    """Start the FastAPI server with K-line processor."""
    
    click.echo(f"🚀 Starting K-line processor with API server on {host}:{port}")
    
    # Create app instance for non-reload mode
    if not reload:
        from api.app import create_app
        app = create_app()
    
    # Run with uvicorn
    uvicorn.run(
        "api.app:create_app" if reload else app,
        host=host,
        port=port,
        reload=reload,
        workers=1 if reload else workers,  # Reload doesn't work with multiple workers
        factory=True if reload else False,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": Settings().log_level,
                "handlers": ["default"],
            },
        }
    )


@cli.command()
def process():
    """Run K-line processor in standalone mode (no API server)."""
    click.echo("🔄 Starting K-line processor in standalone mode...")
    asyncio.run(run_processor())


@cli.command()
@click.option('--host', default='0.0.0.0', help='API server host')
@click.option('--port', default=8088, help='API server port')
def dev(host: str, port: int):
    """Run in development mode with auto-reload."""
    click.echo("🛠️  Starting in development mode with auto-reload...")
    serve.callback(host=host, port=port, reload=True, workers=1)


if __name__ == "__main__":
    import signal
    import os
    
    # Force exit on second Ctrl+C
    original_sigint = signal.getsignal(signal.SIGINT)
    
    def signal_handler(sig, frame):
        print("\n🛑 Shutting down...")
        # Restore original handler to allow force quit on second Ctrl+C
        signal.signal(signal.SIGINT, original_sigint)
        # Force exit
        os._exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    cli()
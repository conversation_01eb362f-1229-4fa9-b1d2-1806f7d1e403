"""
Application lifespan management for FastAPI.

This module handles the startup and shutdown of the FastAPI application,
including initialization of the K-line processor and cleanup.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
import asyncio

from kline_processor.main import KlineProcessor
from kline_processor.config.settings import Settings
from kline_processor.utils.logger import get_logger
from .websocket.redis_subscriber import start_redis_subscriber, set_connection_manager
from .routes.websocket import manager as websocket_manager

logger = get_logger("api.lifespan")

# Global references for dependency injection
processor_instance = None
storage_instance = None
redis_instance = None
redis_subscriber_task = None


@asynccontextmanager
async def lifespan_manager(app: FastAPI):
    """
    Manage application lifespan - startup and shutdown.
    
    This function initializes the K-line processor on startup
    and properly cleans up resources on shutdown.
    """
    global processor_instance, storage_instance, redis_instance, redis_subscriber_task
    
    logger.info("Starting FastAPI application...")
    
    try:
        # Get settings from app state
        settings = app.state.settings if hasattr(app.state, 'settings') else Settings()
        
        # Create and initialize K-line processor without signal handlers
        processor = KlineProcessor(settings, setup_signals=False)
        await processor.initialize()
        
        # Store references for dependency injection
        processor_instance = processor
        storage_instance = processor._storage
        redis_instance = processor._redis_client
        
        # Set connection manager for Redis subscriber
        set_connection_manager(websocket_manager)
        
        # Start Redis subscriber for WebSocket broadcasts
        redis_subscriber_task = asyncio.create_task(
            start_redis_subscriber(redis_instance)
        )
        
        logger.info("FastAPI application started successfully")
        
        yield  # Application runs here
        
    finally:
        logger.info("Shutting down FastAPI application...")
        
        # Cancel Redis subscriber
        if redis_subscriber_task:
            redis_subscriber_task.cancel()
            try:
                await redis_subscriber_task
            except asyncio.CancelledError:
                pass
        
        # Cleanup processor
        if processor_instance:
            await processor_instance.stop()
        
        # Clear references
        processor_instance = None
        storage_instance = None
        redis_instance = None
        
        logger.info("FastAPI application shutdown complete")
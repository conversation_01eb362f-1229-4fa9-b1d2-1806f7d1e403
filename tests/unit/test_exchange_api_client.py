"""
Unit tests for Exchange API client.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import json

from kline_processor.clients.exchange_api_client import (
    ExchangeAPIClient, ExchangeAPIError, RateLimitError, InvalidParameterError
)
from kline_processor.clients.http_client import H<PERSON><PERSON><PERSON>, HTTPClientError
from kline_processor.models.kline import KlineData


@pytest.fixture
def api_client():
    """Create ExchangeAPIClient instance for testing."""
    return ExchangeAPIClient(api_key="test_key", api_secret="test_secret")


@pytest.fixture
def mock_http_client():
    """Create mock HTTPClient."""
    mock = AsyncMock(spec=HTTPClient)
    mock.get.return_value = MagicMock()
    mock.close = AsyncMock()
    mock.set_rate_limit = MagicMock()
    mock.get_stats.return_value = {'total_requests': 0}
    return mock


@pytest.fixture
def sample_kline_response():
    """Create sample K-line API response."""
    return [
        [
            1640995200000,    # Open time
            "45000.12",       # Open
            "45200.00",       # High
            "44950.50",       # Low
            "45123.45",       # Close
            "123.456789",     # Volume
            1640995259999,    # Close time
            "5567890.12",     # Quote asset volume
            1500,             # Number of trades
            "62.123456",      # Taker buy base asset volume
            "2784567.89",     # Taker buy quote asset volume
            "0"               # Ignore
        ]
    ]


class TestExchangeAPIClient:
    """Test cases for ExchangeAPIClient class."""
    
    def test_initialization_mainnet(self):
        """Test ExchangeAPIClient initialization for mainnet."""
        client = ExchangeAPIClient(api_key="key", api_secret="secret")
        
        assert client.api_key == "key"
        assert client.api_secret == "secret"
        assert client.testnet is False
        assert client.base_url == "https://fapi.binance.com"
        assert client.weight_limit == 2400
        assert client.current_weight == 0
    
    def test_initialization_testnet(self):
        """Test ExchangeAPIClient initialization for testnet."""
        client = ExchangeAPIClient(testnet=True)
        
        assert client.testnet is True
        assert client.base_url == "https://testnet.binancefuture.com"
    
    @pytest.mark.asyncio
    async def test_context_manager(self, api_client):
        """Test using ExchangeAPIClient as async context manager."""
        with patch.object(api_client, 'close') as mock_close:
            async with api_client as client:
                assert client is api_client
            
            mock_close.assert_called_once()
    
    def test_check_weight_limit_within_limit(self, api_client):
        """Test weight limit check within limit."""
        api_client.current_weight = 100
        
        # Should not raise exception
        api_client._check_weight_limit(50)
        assert api_client.current_weight == 150
    
    def test_check_weight_limit_exceeds_limit(self, api_client):
        """Test weight limit check when exceeding limit."""
        api_client.current_weight = 2300
        
        with pytest.raises(RateLimitError) as exc_info:
            api_client._check_weight_limit(200)
        
        assert "weight limit exceeded" in str(exc_info.value)
        assert exc_info.value.retry_after is not None
    
    def test_weight_limit_reset(self, api_client):
        """Test weight limit reset after one minute."""
        api_client.current_weight = 1000
        api_client.weight_reset_time = datetime.now() - timedelta(minutes=2)
        
        api_client._check_weight_limit(10)
        
        # Weight should be reset to 10 (not 1010)
        assert api_client.current_weight == 10
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, api_client, mock_http_client):
        """Test successful API request."""
        api_client.http_client = mock_http_client
        
        mock_response = MagicMock()
        mock_response.json.return_value = {"serverTime": 1640995200000}
        mock_http_client.get.return_value = mock_response
        
        result = await api_client._make_request('/fapi/v1/time')
        
        assert result == {"serverTime": 1640995200000}
        assert api_client._total_requests == 1
        mock_http_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_api_error(self, api_client, mock_http_client):
        """Test API request with API error response."""
        api_client.http_client = mock_http_client
        
        mock_response = MagicMock()
        mock_response.json.return_value = {"code": -1121, "msg": "Invalid symbol"}
        mock_http_client.get.return_value = mock_response
        
        with pytest.raises(InvalidParameterError) as exc_info:
            await api_client._make_request('/fapi/v1/klines')
        
        assert "Invalid parameter" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_make_request_rate_limit_error(self, api_client, mock_http_client):
        """Test API request with rate limit error."""
        api_client.http_client = mock_http_client
        
        mock_response = MagicMock()
        mock_response.json.return_value = {"code": -1003, "msg": "Too many requests"}
        mock_http_client.get.return_value = mock_response
        
        with pytest.raises(RateLimitError) as exc_info:
            await api_client._make_request('/fapi/v1/klines')
        
        assert "rate limit exceeded" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_make_request_json_decode_error(self, api_client, mock_http_client):
        """Test API request with invalid JSON response."""
        api_client.http_client = mock_http_client
        
        mock_response = MagicMock()
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_http_client.get.return_value = mock_response
        
        with pytest.raises(ExchangeAPIError) as exc_info:
            await api_client._make_request('/fapi/v1/time')
        
        assert "Invalid JSON response" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_make_request_http_client_error(self, api_client, mock_http_client):
        """Test API request with HTTP client error."""
        api_client.http_client = mock_http_client
        
        mock_http_client.get.side_effect = HTTPClientError("Connection failed")
        
        with pytest.raises(ExchangeAPIError) as exc_info:
            await api_client._make_request('/fapi/v1/time')
        
        assert "HTTP client error" in str(exc_info.value)
        assert api_client._error_count == 1
    
    @pytest.mark.asyncio
    async def test_get_server_time(self, api_client):
        """Test getting server time."""
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = {"serverTime": 1640995200000}
            
            result = await api_client.get_server_time()
            
            assert result == 1640995200000
            mock_request.assert_called_once_with('/fapi/v1/time', weight=1)
    
    @pytest.mark.asyncio
    async def test_get_exchange_info(self, api_client):
        """Test getting exchange information."""
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = {"symbols": []}
            
            result = await api_client.get_exchange_info()
            
            assert result == {"symbols": []}
            mock_request.assert_called_once_with('/fapi/v1/exchangeInfo', weight=1)
    
    @pytest.mark.asyncio
    async def test_get_klines_success(self, api_client, sample_kline_response):
        """Test successful K-line data retrieval."""
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = sample_kline_response
            
            result = await api_client.get_klines("BTCUSDT", "1m", limit=1)
            
            assert len(result) == 1
            assert isinstance(result[0], KlineData)
            assert result[0].s == "BTCUSDT"
            assert result[0].i == "1m"
            assert result[0].t == 1640995200000
            assert result[0].o == "45000.12"
            assert api_client._kline_requests == 1
    
    @pytest.mark.asyncio
    async def test_get_klines_invalid_limit(self, api_client):
        """Test K-line retrieval with invalid limit."""
        with pytest.raises(InvalidParameterError) as exc_info:
            await api_client.get_klines("BTCUSDT", "1m", limit=2000)
        
        assert "Limit cannot exceed 1500" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_klines_missing_parameters(self, api_client):
        """Test K-line retrieval with missing parameters."""
        with pytest.raises(InvalidParameterError) as exc_info:
            await api_client.get_klines("", "1m")
        
        assert "Symbol and interval are required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_klines_with_time_range(self, api_client, sample_kline_response):
        """Test K-line retrieval with time range."""
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = sample_kline_response
            
            start_time = 1640995200000
            end_time = 1640995260000
            
            await api_client.get_klines("BTCUSDT", "1m", 
                                       start_time=start_time, end_time=end_time)
            
            # Check that the request was made with correct parameters
            call_args = mock_request.call_args
            params = call_args[1]['params']
            assert params['startTime'] == start_time
            assert params['endTime'] == end_time
    
    @pytest.mark.asyncio
    async def test_get_klines_weight_calculation(self, api_client, sample_kline_response):
        """Test K-line request weight calculation."""
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = sample_kline_response
            
            # Test different weight calculations
            test_cases = [
                (50, 1),    # <= 100
                (200, 2),   # 101-500
                (800, 5),   # 501-1000
                (1200, 10)  # 1001-1500
            ]
            
            for limit, expected_weight in test_cases:
                mock_request.reset_mock()
                await api_client.get_klines("BTCUSDT", "1m", limit=limit)
                
                call_args = mock_request.call_args
                assert call_args[1]['weight'] == expected_weight
    
    @pytest.mark.asyncio
    async def test_get_klines_parse_error(self, api_client):
        """Test K-line parsing with invalid data."""
        invalid_kline_data = [
            [1640995200000, "invalid", "data"]  # Missing required fields
        ]
        
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = invalid_kline_data
            
            result = await api_client.get_klines("BTCUSDT", "1m")
            
            # Should return empty list due to parsing error
            assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_get_klines_batch(self, api_client, sample_kline_response):
        """Test batch K-line retrieval."""
        with patch.object(api_client, 'get_klines') as mock_get_klines:
            # Mock get_klines to return sample data
            mock_get_klines.return_value = [
                KlineData(
                    t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
                    f=0, L=0, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
                    v="123.456789", n=1500, x=True, q="5567890.12",
                    V="62.123456", Q="2784567.89", B="0"
                )
            ]
            
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            result = await api_client.get_klines_batch(symbols, "1m", limit=100)
            
            assert len(result) == 3
            assert "BTCUSDT" in result
            assert "ETHUSDT" in result
            assert "ADAUSDT" in result
            assert mock_get_klines.call_count == 3
    
    @pytest.mark.asyncio
    async def test_get_klines_batch_with_errors(self, api_client):
        """Test batch K-line retrieval with some failures."""
        with patch.object(api_client, 'get_klines') as mock_get_klines:
            # Mock get_klines to fail for one symbol
            def mock_get_klines_side_effect(symbol, *args, **kwargs):
                if symbol == "INVALID":
                    raise ExchangeAPIError("Invalid symbol")
                return []
            
            mock_get_klines.side_effect = mock_get_klines_side_effect
            
            symbols = ["BTCUSDT", "INVALID", "ETHUSDT"]
            result = await api_client.get_klines_batch(symbols, "1m")
            
            # Should have results for valid symbols, empty list for invalid
            assert len(result) == 3
            assert "BTCUSDT" in result
            assert "INVALID" in result
            assert "ETHUSDT" in result
            assert result["INVALID"] == []  # Empty due to error
    
    def test_get_stats(self, api_client):
        """Test getting client statistics."""
        api_client._total_requests = 50
        api_client._kline_requests = 30
        api_client._error_count = 2
        api_client.current_weight = 150
        
        with patch.object(api_client.http_client, 'get_stats') as mock_http_stats:
            mock_http_stats.return_value = {'http_requests': 50}
            
            stats = api_client.get_stats()
            
            assert stats['total_requests'] == 50
            assert stats['kline_requests'] == 30
            assert stats['error_count'] == 2
            assert stats['current_weight'] == 150
            assert stats['weight_limit'] == 2400
            assert stats['base_url'] == "https://fapi.binance.com"
            assert stats['testnet'] is False
            assert 'http_client_stats' in stats
    
    @pytest.mark.asyncio
    async def test_close(self, api_client):
        """Test closing API client."""
        with patch.object(api_client.http_client, 'close') as mock_close:
            await api_client.close()
            mock_close.assert_called_once()
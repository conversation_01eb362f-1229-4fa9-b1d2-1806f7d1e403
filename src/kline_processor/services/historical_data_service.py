"""
Simple historical data service matching TypeScript implementation.

This service provides K-line data fetching with straightforward logic.
"""

import asyncio
import json
from typing import List, Dict, Optional
from datetime import datetime, timezone
from dataclasses import dataclass

from ..clients.exchange_api_client_v2 import ExchangeAPIClientV2, ExchangeAPIError
from ..storage.kline_storage import KlineStorage
from ..models.kline import KlineData
from ..utils.logger import get_logger


@dataclass
class FetchResult:
    """Result of historical data fetch operation."""
    symbol: str
    interval: str
    klines_fetched: int
    success: bool
    error: Optional[str] = None


class HistoricalDataService:
    """
    Service for fetching historical K-line data.
    
    Simple implementation that fetches the most recent N K-lines.
    """
    
    def __init__(self, 
                 exchange_client: Optional[ExchangeAPIClientV2] = None,
                 storage: Optional[KlineStorage] = None,
                 settings = None):
        """
        Initialize historical data service.
        
        Args:
            exchange_client: Exchange API client instance
            storage: K-line storage instance
            settings: Application settings
        """
        self.exchange_client = exchange_client or ExchangeAPIClientV2()
        self.storage = storage or KlineStorage()
        self.logger = get_logger(self.__class__.__name__)
        
        # Import here to avoid circular imports
        from ..config.settings import Settings
        self.settings = settings or Settings()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close the service and cleanup resources."""
        try:
            await self.exchange_client.close()
            self.logger.info("Historical data service closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing historical data service: {e}")
    
    async def sync_klines(self,
                         symbol: str,
                         interval: str = "1m",
                         target_count: int = 1000,
                         count: int = 5) -> FetchResult:
        """
        Sync K-lines - fetch historical data in multiple iterations.
        
        Args:
            symbol: Trading symbol
            interval: Time interval (default: 1m)
            target_count: Number of K-lines to fetch per iteration
            count: Number of iterations to perform
            
        Returns:
            FetchResult with sync statistics
        """
        try:
            self.logger.info(f"Starting sync for {symbol}:{interval} - {count} iterations of {target_count} klines each")
            self.logger.info(f"Settings: historical_rate_limit_delay={self.settings.historical_rate_limit_delay}")
            
            total_fetched = 0
            total_stored = 0
            end_time = None
            batch_size = min(target_count, 1500)  # API limit is 1500
            current_count = count
            
            # Loop through iterations like the TypeScript code
            while current_count > 0:
                # Fetch K-lines
                klines = await self.exchange_client.get_continuous_klines(
                    symbol=symbol,
                    interval=interval,
                    limit=batch_size,
                    end_time=end_time
                )
                
                if not klines or len(klines) == 0:
                    self.logger.warning(f"No data returned for {symbol}:{interval}")
                    break
                
                self.logger.info(f"Successfully fetched and processing {symbol} {interval} iteration {count - current_count + 1} with {len(klines)} records")
                
                # Debug: Check data type
                if klines:
                    self.logger.debug(f"First kline type: {type(klines[0])}")
                    if hasattr(klines[0], '__dict__'):
                        self.logger.debug(f"First kline attributes: {klines[0].__dict__}")
                
                # Process and store klines
                for i, kline in enumerate(klines):
                    # Convert kline format if needed
                    # Check if kline is a KlineData object or dict
                    if hasattr(kline, 'open_time'):
                        # It's a KlineData object
                        kline_data = {
                            't': kline.open_time,
                            'T': kline.close_time,
                            's': symbol,
                            'i': interval,
                            'f': 0,
                            'L': 0,
                            'o': kline.open,
                            'c': kline.close,
                            'h': kline.high,
                            'l': kline.low,
                            'v': kline.volume,
                            'n': kline.trade_count,
                            'x': kline.is_closed,
                            'q': kline.quote_volume,
                            'V': kline.taker_buy_volume,
                            'Q': kline.taker_buy_quote_volume,
                            'B': "0"
                        }
                        open_time = kline.open_time
                    else:
                        # It's a dict
                        kline_data = {
                            't': kline['open_time'],
                            'T': kline['close_time'],
                            's': symbol,
                            'i': interval,
                            'f': 0,
                            'L': 0,
                            'o': kline['open'],
                            'c': kline['close'],
                            'h': kline['high'],
                            'l': kline['low'],
                            'v': kline['volume'],
                            'n': kline['trade_count'],
                            'x': kline['is_closed'],
                            'q': kline['quote_volume'],
                            'V': kline['taker_buy_volume'],
                            'Q': kline['taker_buy_quote_volume'],
                            'B': "0"
                        }
                        open_time = kline['open_time']
                    
                    # Remove existing entry with same timestamp and add new one
                    key = f"kline:{symbol}:{interval}"
                    await self.redis_client.client.zremrangebyscore(key, open_time, open_time)
                    
                    # Convert dict to JSON string for storage
                    await self.redis_client.client.zadd(key, {json.dumps(kline_data): open_time})
                    total_stored += 1
                
                total_fetched += len(klines)
                
                # Decrement count and prepare for next iteration
                current_count -= 1
                if current_count > 0 and len(klines) > 0:
                    # Set end_time to the oldest kline's open time for next iteration
                    # Binance returns data from newest to oldest, so the last element is the oldest
                    last_kline = klines[-1]
                    if hasattr(last_kline, 'open_time'):
                        end_time = last_kline.open_time - 1  # Subtract 1 to avoid duplicate
                    else:
                        end_time = last_kline['open_time'] - 1
                    
                    # Add delay to avoid rate limits
                    await asyncio.sleep(self.settings.historical_rate_limit_delay)
            
            self.logger.info(f"Sync completed for {symbol}:{interval}: {total_stored} klines stored")
            
            return FetchResult(
                symbol=symbol,
                interval=interval,
                klines_fetched=total_fetched,
                success=True,
                error=None
            )
            
        except Exception as e:
            import traceback
            self.logger.error(f"Sync failed for {symbol}:{interval}: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return FetchResult(
                symbol=symbol,
                interval=interval,
                klines_fetched=0,
                success=False,
                error=str(e)
            )
    
    async def sync_multiple_symbols(self,
                                  symbols: List[str],
                                  interval: str = "1m",
                                  target_count: int = 1000) -> List[FetchResult]:
        """
        Sync K-lines for multiple symbols.
        
        Args:
            symbols: List of trading symbols
            interval: Time interval
            target_count: Number of K-lines to fetch per symbol
            
        Returns:
            List of fetch results
        """
        results = []
        
        for symbol in symbols:
            result = await self.sync_klines(symbol, interval, target_count=target_count)
            results.append(result)
            
            # Add small delay between symbols to avoid rate limits
            if symbol != symbols[-1]:  # Not the last symbol
                await asyncio.sleep(0.5)
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        total_klines = sum(r.klines_fetched for r in results)
        
        self.logger.info(f"Sync completed: {successful}/{len(symbols)} successful, "
                        f"{total_klines} total K-lines fetched")
        
        return results


# Global service instance
historical_data_service = HistoricalDataService()
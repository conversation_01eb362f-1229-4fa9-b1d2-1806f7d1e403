"""
Health check endpoints for monitoring and status.
"""

from fastapi import APIRouter, Depends
from typing import Dict, Any

from ..dependencies import get_processor, get_redis
from kline_processor.main import KlineProcessor
from kline_processor.storage.redis_client import RedisClient

router = APIRouter()


@router.get("/health")
async def health_check(
    processor: KlineProcessor = Depends(get_processor),
    redis: RedisClient = Depends(get_redis)
) -> Dict[str, Any]:
    """
    Perform comprehensive health check.
    
    Returns:
        Dictionary with health status and service information
    """
    health_result = await processor.health_check()
    
    return {
        "status": "healthy" if health_result["healthy"] else "unhealthy",
        "services": health_result.get("services", {}),
        "issues": health_result.get("issues", []),
        "application_running": health_result.get("application_running", False)
    }


@router.get("/stats")
async def get_statistics(
    processor: KlineProcessor = Depends(get_processor)
) -> Dict[str, Any]:
    """
    Get application statistics.
    
    Returns:
        Dictionary with comprehensive application statistics
    """
    return processor.get_application_statistics()
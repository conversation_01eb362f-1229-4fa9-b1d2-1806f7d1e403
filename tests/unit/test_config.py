"""
Unit tests for configuration management.
"""

import pytest
from unittest.mock import patch
import os
from pydantic import ValidationError

from kline_processor.config.settings import Settings


class TestSettings:
    """Test cases for Settings class."""
    
    def test_default_settings(self):
        """Test default configuration values."""
        settings = Settings()
        
        assert settings.kline_url_v2 == "https://fapi.binance.com/fapi/v1/continuousKlines"
        assert settings.base_url == "wss://fstream.binance.com"
        assert settings.redis_host == "127.0.0.1"
        assert settings.redis_port == 6379
        assert settings.redis_password == "valkey_password"
        assert settings.redis_db == 3
        assert settings.websocket_chunk_size == 20
        assert settings.max_retries == 5
        assert settings.log_level == "INFO"
        
    def test_environment_variable_override(self):
        """Test configuration from environment variables."""
        with patch.dict(os.environ, {
            'REDIS_HOST': 'redis.example.com',
            'REDIS_PORT': '6380',
            'LOG_LEVEL': 'DEBUG'
        }):
            settings = Settings()
            assert settings.redis_host == 'redis.example.com'
            assert settings.redis_port == 6380
            assert settings.log_level == 'DEBUG'
    
    def test_timeframes_parsing(self):
        """Test parsing of comma-separated timeframes."""
        with patch.dict(os.environ, {
            'TIMEFRAMES': '1m,5m,1h,1d'
        }):
            settings = Settings()
            assert settings.timeframes == ['1m', '5m', '1h', '1d']
    
    def test_symbols_parsing(self):
        """Test parsing of comma-separated symbols."""
        with patch.dict(os.environ, {
            'SYMBOLS': 'btcusdt,ethusdt,adausdt'
        }):
            settings = Settings()
            assert settings.symbols == ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
    
    def test_invalid_log_level(self):
        """Test validation error for invalid log level."""
        with patch.dict(os.environ, {'LOG_LEVEL': 'INVALID'}):
            with pytest.raises(ValidationError):
                Settings()
    
    def test_redis_port_validation(self):
        """Test validation of Redis port range."""
        with patch.dict(os.environ, {'REDIS_PORT': '70000'}):
            with pytest.raises(ValidationError):
                Settings()
    
    def test_get_redis_url(self):
        """Test Redis URL generation with password."""
        settings = Settings()
        expected_url = f"redis://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/{settings.redis_db}"
        assert settings.get_redis_url() == expected_url
    
    def test_get_redis_url_no_password(self):
        """Test Redis URL generation without password."""
        settings = Settings(redis_password=None)
        expected_url = f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}"
        assert settings.get_redis_url() == expected_url
    
    def test_get_websocket_url(self):
        """Test WebSocket URL generation."""
        settings = Settings()
        streams = ['btcusdt@kline_1m', 'ethusdt@kline_1m']
        expected_url = f"{settings.base_url}/stream?streams=btcusdt@kline_1m/ethusdt@kline_1m"
        assert settings.get_websocket_url(streams) == expected_url
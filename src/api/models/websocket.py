"""
WebSocket message models for real-time K-line streaming.

This module defines the message protocol for WebSocket communication
including subscription management and data updates.
"""

from pydantic import BaseModel, Field
from typing import Literal, Optional
from .kline import KlineData


class WSMessage(BaseModel):
    """Base WebSocket message model."""
    
    action: Literal["subscribe", "unsubscribe", "ping"] = Field(
        ...,
        description="Action to perform"
    )


class WSSubscribeMessage(WSMessage):
    """Subscribe to K-line updates for a symbol and interval."""
    
    action: Literal["subscribe"] = "subscribe"
    symbol: str = Field(
        ...,
        description="Trading pair symbol to subscribe",
        examples=["BTCUSDT", "ETHUSDT"]
    )
    interval: Literal["1m", "3m", "5m", "15m", "30m", "1h", "4h", "1d"] = Field(
        ...,
        description="K-line interval to subscribe"
    )
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "action": "subscribe",
                "symbol": "BTCUSDT",
                "interval": "1m"
            }
        }
    }


class WSUnsubscribeMessage(WSMessage):
    """Unsubscribe from K-line updates."""
    
    action: Literal["unsubscribe"] = "unsubscribe"
    symbol: str = Field(
        ...,
        description="Trading pair symbol to unsubscribe",
        examples=["BTCUSDT", "ETHUSDT"]
    )
    interval: Literal["1m", "3m", "5m", "15m", "30m", "1h", "4h", "1d"] = Field(
        ...,
        description="K-line interval to unsubscribe"
    )
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "action": "unsubscribe",
                "symbol": "BTCUSDT",
                "interval": "1m"
            }
        }
    }


class WSDataMessage(BaseModel):
    """K-line data update message sent to clients."""
    
    type: Literal["kline"] = "kline"
    data: KlineData = Field(..., description="K-line data update")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "type": "kline",
                "data": KlineData.model_config["json_schema_extra"]["example"]
            }
        }
    }


class WSErrorMessage(BaseModel):
    """Error message sent to clients."""
    
    type: Literal["error"] = "error"
    message: str = Field(..., description="Error description")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "type": "error",
                "message": "Invalid subscription parameters"
            }
        }
    }


class WSStatusMessage(BaseModel):
    """Status message for connection and subscription events."""
    
    type: Literal["connected", "subscribed", "unsubscribed", "pong"]
    message: Optional[str] = Field(None, description="Optional status message")
    symbol: Optional[str] = Field(None, description="Related symbol")
    interval: Optional[str] = Field(None, description="Related interval")
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "type": "connected",
                    "message": "Connected to K-line WebSocket"
                },
                {
                    "type": "subscribed",
                    "symbol": "BTCUSDT",
                    "interval": "1m"
                }
            ]
        }
    }
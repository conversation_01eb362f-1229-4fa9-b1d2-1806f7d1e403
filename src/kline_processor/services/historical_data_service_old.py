"""
Historical data service matching TypeScript implementation.

This service provides K-line data fetching using continuous API
with recursive fetching and batch storage.
"""

import asyncio
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass

from ..clients.exchange_api_client_v2 import ExchangeAPIClientV2, ExchangeAPIError, RateLimitError
from ..storage.kline_storage import KlineStorage
from ..models.kline import KlineData
from ..utils.logger import get_logger


@dataclass
class FetchResult:
    """Result of historical data fetch operation."""
    symbol: str
    interval: str
    klines_fetched: int
    success: bool
    error: Optional[str] = None


class HistoricalDataService:
    """
    Service for fetching historical K-line data matching TypeScript implementation.
    
    This service uses continuous API with recursive fetching backwards from current time.
    """
    
    def __init__(self, 
                 exchange_client: Optional[ExchangeAPIClientV2] = None,
                 storage: Optional[KlineStorage] = None):
        """
        Initialize historical data service.
        
        Args:
            exchange_client: Exchange API client instance
            storage: K-line storage instance
        """
        self.exchange_client = exchange_client or ExchangeAPIClientV2()
        self.storage = storage or KlineStorage()
        self.logger = get_logger(self.__class__.__name__)
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close the service and cleanup resources."""
        try:
            await self.exchange_client.close()
            self.logger.info("Historical data service closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing historical data service: {e}")
    
    async def sync_klines(self,
                         symbol: str,
                         interval: str = "1m",
                         skip_if_exists: int = 50,
                         target_count: int = 1000) -> FetchResult:
        """
        Sync K-lines matching TypeScript logic.
        
        Args:
            symbol: Trading symbol
            interval: Time interval (default: 1m)
            skip_if_exists: Skip sync if this many K-lines already exist
            
        Returns:
            FetchResult with sync statistics
        """
        try:
            # Check if we should skip sync (matching TypeScript)
            existing_count = await self.storage.get_kline_count(symbol, interval)
            if existing_count > skip_if_exists:
                self.logger.info(f"Skipping sync for {symbol}:{interval} - already has {existing_count} klines")
                return FetchResult(
                    symbol=symbol,
                    interval=interval,
                    klines_fetched=0,
                    success=True,
                    error=f"Skipped - already has {existing_count} klines"
                )
            
            self.logger.info(f"Syncing klines for {symbol}:{interval}")
            
            # Use recursive fetching with target count limit
            all_klines = await self._fetch_klines_recursive(symbol, interval, target_count=target_count)
            
            if not all_klines:
                return FetchResult(
                    symbol=symbol,
                    interval=interval,
                    klines_fetched=0,
                    success=False,
                    error="No data fetched"
                )
            
            # Store data in batches like TypeScript (batches of 10)
            stored_count = 0
            batch_size = 10
            
            for i in range(0, len(all_klines), batch_size):
                batch = all_klines[i:i + batch_size]
                # Store without publishing to avoid real-time processing during sync
                count = await self.storage.store_klines_batch(batch, publish=False)
                stored_count += count
                self.logger.debug(f"Stored batch of {len(batch)} klines")
            
            self.logger.info(f"Sync completed for {symbol}:{interval}: {stored_count} klines stored")
            
            return FetchResult(
                symbol=symbol,
                interval=interval,
                klines_fetched=len(all_klines),
                success=True,
                error=None
            )
            
        except Exception as e:
            self.logger.error(f"Sync failed for {symbol}:{interval}: {e}")
            return FetchResult(
                symbol=symbol,
                interval=interval,
                klines_fetched=0,
                success=False,
                error=str(e)
            )
    
    async def _fetch_klines_recursive(self,
                                    symbol: str,
                                    interval: str,
                                    end_time: Optional[int] = None,
                                    accumulated: List[KlineData] = None,
                                    target_count: Optional[int] = None) -> List[KlineData]:
        """
        Recursively fetch K-lines backwards from end_time matching TypeScript implementation.
        
        Args:
            symbol: Trading symbol
            interval: Time interval
            end_time: End time for this batch (None = current time)
            accumulated: Previously fetched K-lines
            
        Returns:
            All fetched K-lines sorted by timestamp
        """
        if accumulated is None:
            accumulated = []
        
        # Check if we already have enough data
        if target_count and len(accumulated) >= target_count:
            self.logger.info(f"Already have {len(accumulated)} klines, target was {target_count}")
            return accumulated[-target_count:]  # Return the most recent target amount
        
        try:
            # Calculate how many more we need
            remaining = target_count - len(accumulated) if target_count else 1500
            limit = min(1500, remaining)  # API max is 1500
            
            # Fetch batch
            klines = await self.exchange_client.get_continuous_klines(
                symbol=symbol,
                interval=interval,
                limit=limit,
                end_time=end_time
            )
            
            if not klines:
                # No more data
                return accumulated
            
            self.logger.info(f"Fetched {len(klines)} klines for {symbol}:{interval}")
            
            # Add to accumulated (front, since we're going backwards)
            accumulated = klines + accumulated
            
            # Check if we have enough data now
            if target_count and len(accumulated) >= target_count:
                self.logger.info(f"Reached target count: {len(accumulated)} klines")
                # Return the most recent target_count klines
                return accumulated[-target_count:]
            
            # If we got full batch and still need more, continue recursively
            if len(klines) == limit and limit == 1500:
                oldest_time = klines[0].t
                self.logger.debug(f"Need more data, fetching before {oldest_time}")
                return await self._fetch_klines_recursive(
                    symbol=symbol,
                    interval=interval,
                    end_time=oldest_time - 1,
                    accumulated=accumulated,
                    target_count=target_count
                )
            
            return accumulated
            
        except Exception as e:
            self.logger.error(f"Recursive fetch failed: {e}")
            # Return what we have so far
            return accumulated
    
    async def sync_multiple_symbols(self,
                                  symbols: List[str],
                                  interval: str = "1m",
                                  target_count: int = 1000) -> List[FetchResult]:
        """
        Sync K-lines for multiple symbols.
        
        Args:
            symbols: List of trading symbols
            interval: Time interval
            
        Returns:
            List of fetch results
        """
        results = []
        
        for symbol in symbols:
            result = await self.sync_klines(symbol, interval, target_count=target_count)
            results.append(result)
            
            # Add small delay between symbols to avoid rate limits
            if symbol != symbols[-1]:  # Not the last symbol
                await asyncio.sleep(0.5)
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        total_klines = sum(r.klines_fetched for r in results)
        
        self.logger.info(f"Sync completed: {successful}/{len(symbols)} successful, "
                        f"{total_klines} total K-lines fetched")
        
        return results


# Global service instance
historical_data_service = HistoricalDataService()
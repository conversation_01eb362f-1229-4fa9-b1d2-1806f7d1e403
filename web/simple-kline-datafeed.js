/**
 * Simple K-line Datafeed - Works with SimpleWebSocketManager
 * Each subscription change creates a new WebSocket connection
 */

class SimpleKlineDatafeed {
    constructor(apiUrl, wsUrl) {
        this.apiUrl = apiUrl;
        this.wsUrl = wsUrl;
        this.wsManager = null;
        
        // Track TradingView subscribers
        this.subscribers = new Map(); // subscriberUID → {symbol, interval, callback, key}
        
        // Current active subscription
        this.currentSubscriptionKey = null;
        
        // Cache for symbol info
        this.symbolCache = new Map();
    }

    /**
     * Initialize WebSocket manager
     */
    initWebSocket(onRealtimeUpdate) {
        this.wsManager = new SimpleWebSocketManager(this.wsUrl, onRealtimeUpdate);
    }

    /**
     * TradingView Datafeed API: onReady
     */
    onReady(callback) {
        setTimeout(() => {
            // Get supported resolutions from config
            const resolutions = appConfig.intervals.map(i => i.value);
            callback({
                supported_resolutions: resolutions,
                supports_marks: false,
                supports_timescale_marks: false,
                supports_time: true,
                debug: true
            });
        }, 0);
    }

    /**
     * TradingView Datafeed API: searchSymbols
     */
    searchSymbols(userInput, exchange, symbolType, onResultReadyCallback) {
        // Generate symbol info from config
        const symbols = appConfig.symbols.map(symbol => {
            // Extract base and quote currency
            let base = symbol.substring(0, 3);
            let quote = symbol.substring(3);
            
            // Special cases for longer base symbols
            if (symbol.includes('USDT')) {
                base = symbol.replace('USDT', '');
                quote = 'USDT';
            } else if (symbol.includes('BUSD')) {
                base = symbol.replace('BUSD', '');
                quote = 'BUSD';
            }
            
            return {
                symbol: symbol,
                full_name: `${base}/${quote}`,
                description: `${base} / ${quote}`,
                type: 'crypto',
                exchange: 'Binance'
            };
        });
        
        const results = symbols.filter(s => 
            s.symbol.toLowerCase().includes(userInput.toLowerCase()) ||
            s.description.toLowerCase().includes(userInput.toLowerCase())
        );
        
        onResultReadyCallback(results);
    }

    /**
     * TradingView Datafeed API: resolveSymbol
     */
    resolveSymbol(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
        // Check cache first
        if (this.symbolCache.has(symbolName)) {
            setTimeout(() => onSymbolResolvedCallback(this.symbolCache.get(symbolName)), 0);
            return;
        }
        
        // Get supported resolutions from config
        const resolutions = appConfig.intervals.map(i => i.value);
        
        const symbolInfo = {
            name: symbolName,
            ticker: symbolName,
            description: symbolName,
            type: 'crypto',
            session: '24x7',
            timezone: 'Etc/UTC',
            minmov: 1,
            pricescale: symbolName.includes('BTC') ? 100 : 10000,
            has_intraday: true,
            has_daily: true,
            has_weekly_and_monthly: true,
            supported_resolutions: resolutions,
            data_status: 'streaming',
            currency_code: 'USD'
        };
        
        // Cache the symbol info
        this.symbolCache.set(symbolName, symbolInfo);
        
        setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
    }

    /**
     * TradingView Datafeed API: getBars
     * Fetch historical data
     */
    async getBars(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) {
        const { from, to, firstDataRequest } = periodParams;
        
        console.log(`[SimpleDatafeed] Getting bars for ${symbolInfo.name}, resolution: ${resolution}`);
        
        try {
            // Find the interval config for this resolution
            const intervalConfig = appConfig.intervals.find(i => i.value === resolution);
            const interval = intervalConfig ? intervalConfig.api_interval : '1m';
            
            const response = await fetch(`${this.apiUrl}/api/v1/klines?symbol=${symbolInfo.name}&interval=${interval}&start_time=${from * 1000}&end_time=${to * 1000}&limit=1000`);
            const data = await response.json();
            
            if (data.success && data.data) {
                const bars = data.data.map(kline => ({
                    time: kline.ot,
                    open: parseFloat(kline.o),
                    high: parseFloat(kline.h),
                    low: parseFloat(kline.l),
                    close: parseFloat(kline.c),
                    volume: parseFloat(kline.v)
                }));
                
                console.log(`[SimpleDatafeed] Loaded ${bars.length} bars`);
                onHistoryCallback(bars, { noData: bars.length === 0 });
            } else {
                console.error('[SimpleDatafeed] Failed to load historical data:', data);
                onHistoryCallback([], { noData: true });
            }
        } catch (error) {
            console.error('[SimpleDatafeed] Error fetching klines:', error);
            onErrorCallback(error);
        }
    }

    /**
     * TradingView Datafeed API: subscribeBars
     * Subscribe to real-time updates
     */
    async subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
        console.log(`[SimpleDatafeed] subscribeBars called - Symbol: ${symbolInfo.name}, Resolution: ${resolution}, UID: ${subscriberUID}`);
        
        // Find the interval config for this resolution
        const intervalConfig = appConfig.intervals.find(i => i.value === resolution);
        const interval = intervalConfig ? intervalConfig.api_interval : '1m';
        const newKey = `${symbolInfo.name}@${interval}`;
        
        // Store subscriber info
        this.subscribers.set(subscriberUID, {
            symbol: symbolInfo.name,
            interval: interval,
            callback: onRealtimeCallback,
            key: newKey,
            onResetCacheNeeded: onResetCacheNeededCallback
        });
        
        // If this is a new subscription, create new WebSocket connection
        if (this.currentSubscriptionKey !== newKey) {
            console.log(`[SimpleDatafeed] Switching from ${this.currentSubscriptionKey} to ${newKey}`);
            this.currentSubscriptionKey = newKey;
            
            try {
                await this.wsManager.subscribe(symbolInfo.name, interval);
                console.log(`[SimpleDatafeed] Successfully subscribed to ${newKey}`);
            } catch (error) {
                console.error(`[SimpleDatafeed] Failed to subscribe to ${newKey}:`, error);
                if (onResetCacheNeededCallback) {
                    onResetCacheNeededCallback();
                }
            }
        } else {
            console.log(`[SimpleDatafeed] Already subscribed to ${newKey}`);
        }
    }

    /**
     * TradingView Datafeed API: unsubscribeBars
     */
    async unsubscribeBars(subscriberUID) {
        console.log(`[SimpleDatafeed] unsubscribeBars called - UID: ${subscriberUID}`);
        
        const subscriber = this.subscribers.get(subscriberUID);
        if (!subscriber) {
            console.warn(`[SimpleDatafeed] No subscriber found for UID: ${subscriberUID}`);
            return;
        }
        
        // Remove from subscribers map
        this.subscribers.delete(subscriberUID);
        
        // Check if there are other subscribers for the same key
        const hasOtherSubscribers = Array.from(this.subscribers.values()).some(sub => sub.key === subscriber.key);
        
        if (!hasOtherSubscribers) {
            console.log(`[SimpleDatafeed] No more subscribers for ${subscriber.key}, closing WebSocket`);
            
            // Close WebSocket connection
            if (this.wsManager) {
                this.wsManager.unsubscribe(subscriber.symbol, subscriber.interval);
            }
            
            // Clear current subscription
            if (this.currentSubscriptionKey === subscriber.key) {
                this.currentSubscriptionKey = null;
            }
        } else {
            console.log(`[SimpleDatafeed] Other subscribers exist for ${subscriber.key}, keeping connection`);
        }
    }

    /**
     * Handle real-time updates from WebSocket
     */
    onRealtimeUpdate(key, bar) {
        // Find all subscribers for this symbol@interval
        let updateCount = 0;
        
        for (const [uid, subscriber] of this.subscribers) {
            if (subscriber.key === key) {
                try {
                    subscriber.callback(bar);
                    updateCount++;
                } catch (error) {
                    console.error(`[SimpleDatafeed] Error calling subscriber callback for ${uid}:`, error);
                }
            }
        }
        
        console.log(`[SimpleDatafeed] Delivered update for ${key} to ${updateCount} subscribers`);
    }

    /**
     * Get debug information
     */
    getDebugInfo() {
        return {
            currentSubscriptionKey: this.currentSubscriptionKey,
            subscriberCount: this.subscribers.size,
            subscribers: Array.from(this.subscribers.entries()).map(([uid, sub]) => ({
                uid: uid,
                key: sub.key,
                symbol: sub.symbol,
                interval: sub.interval
            })),
            wsManagerDebug: this.wsManager ? this.wsManager.getDebugInfo() : null
        };
    }

    /**
     * Clean up resources
     */
    destroy() {
        // Clear all subscribers
        this.subscribers.clear();
        this.symbolCache.clear();
        this.currentSubscriptionKey = null;
        
        // Close WebSocket connection
        if (this.wsManager) {
            this.wsManager.close();
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleKlineDatafeed;
}
"""
Dependency injection providers for FastAPI routes.

This module provides functions to access core services
through FastAPI's dependency injection system.
"""

from typing import Optional
from fastapi import HTTPException

from kline_processor.main import KlineProcessor
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.storage.redis_client import RedisClient
from . import lifespan


async def get_processor() -> KlineProcessor:
    """
    Get KlineProcessor instance.
    
    Raises:
        HTTPException: If processor is not initialized
    """
    if not lifespan.processor_instance:
        raise HTTPException(status_code=503, detail="K-line processor not initialized")
    return lifespan.processor_instance


async def get_storage() -> KlineStorage:
    """
    Get KlineStorage instance.
    
    Raises:
        HTTPException: If storage is not initialized
    """
    if not lifespan.storage_instance:
        raise HTTPException(status_code=503, detail="Storage service not initialized")
    return lifespan.storage_instance


async def get_redis() -> RedisClient:
    """
    Get Redis client instance.
    
    Raises:
        HTTPException: If Redis client is not initialized
    """
    if not lifespan.redis_instance:
        raise HTTPException(status_code=503, detail="Redis client not initialized")
    return lifespan.redis_instance
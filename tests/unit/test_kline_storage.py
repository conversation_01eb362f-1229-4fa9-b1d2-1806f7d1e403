"""
Unit tests for K-line storage operations.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from kline_processor.storage.kline_storage import KlineStorage, KlineStorageError
from kline_processor.storage.redis_client import RedisClient
from kline_processor.models.kline import KlineData


@pytest.fixture
def mock_redis_client():
    """Create a mock RedisClient instance."""
    mock = AsyncMock(spec=RedisClient)
    mock.zadd.return_value = 1
    mock.set.return_value = True
    mock.get.return_value = None
    mock.zcard.return_value = 0
    mock.zrangebyscore.return_value = []
    mock.zrange.return_value = []
    mock.zrem.return_value = 0
    mock.zremrangebyrank = AsyncMock(return_value=0)
    mock.zremrangebyscore = AsyncMock(return_value=0)
    mock.delete.return_value = 1
    mock.publish.return_value = 1
    mock.keys.return_value = []
    mock._get_connection.return_value.__aenter__ = AsyncMock()
    mock._get_connection.return_value.__aexit__ = AsyncMock()
    return mock


@pytest.fixture
def kline_storage(mock_redis_client):
    """Create KlineStorage instance with mock Redis client."""
    return KlineStorage(redis_client=mock_redis_client)


@pytest.fixture
def sample_kline():
    """Create sample K-line data for testing."""
    return KlineData(
        t=1640995200000,
        T=1640995259999,
        s="BTCUSDT",
        i="1m",
        f=1234567890,
        L=1234567899,
        o="45000.12",
        c="45123.45",
        h="45200.00",
        l="44950.50",
        v="123.456789",
        n=1500,
        x=True,
        q="5567890.12",
        V="62.123456",
        Q="2784567.89",
        B="0"
    )


class TestKlineStorage:
    """Test cases for KlineStorage class."""
    
    def test_initialization(self, mock_redis_client):
        """Test KlineStorage initialization."""
        storage = KlineStorage(redis_client=mock_redis_client)
        
        assert storage.redis_client is mock_redis_client
        assert storage.max_klines_per_symbol == 10000
        assert storage.cleanup_batch_size == 1000
        assert storage.expiry_days == 30
    
    def test_key_generation(self, kline_storage):
        """Test Redis key generation methods."""
        symbol = "BTCUSDT"
        interval = "1m"
        
        kline_key = kline_storage._get_kline_key(symbol, interval)
        assert kline_key == "kline:BTCUSDT:1m"
        
        latest_key = kline_storage._get_latest_key(symbol, interval)
        assert latest_key == "latest:BTCUSDT:1m"
        
        meta_key = kline_storage._get_meta_key(symbol, interval)
        assert meta_key == "meta:BTCUSDT:1m"
        
        channel = kline_storage._get_kline_channel(symbol, interval)
        assert channel == "kline_updates:BTCUSDT:1m"
    
    def test_key_generation_lowercase_symbol(self, kline_storage):
        """Test key generation with lowercase symbol."""
        symbol = "btcusdt"
        interval = "1m"
        
        kline_key = kline_storage._get_kline_key(symbol, interval)
        assert kline_key == "kline:BTCUSDT:1m"
    
    @pytest.mark.asyncio
    async def test_store_kline_success(self, kline_storage, sample_kline, mock_redis_client):
        """Test successful K-line storage."""
        result = await kline_storage.store_kline(sample_kline, publish=False)
        
        assert result is True
        
        # Verify Redis operations were called
        mock_redis_client.zadd.assert_called_once()
        mock_redis_client.set.assert_called()  # Called twice (latest + meta)
        
        # Check zadd was called with correct parameters
        zadd_call = mock_redis_client.zadd.call_args
        assert zadd_call[0][0] == "kline:BTCUSDT:1m"  # key
        assert len(zadd_call[0][1]) == 1  # mapping with one item
    
    @pytest.mark.asyncio
    async def test_store_kline_with_publish(self, kline_storage, sample_kline, mock_redis_client):
        """Test K-line storage with publish notification."""
        result = await kline_storage.store_kline(sample_kline, publish=True)
        
        assert result is True
        mock_redis_client.publish.assert_called()  # Should be called twice (specific + global)
        assert mock_redis_client.publish.call_count == 2
    
    @pytest.mark.asyncio
    async def test_store_kline_failure(self, kline_storage, sample_kline, mock_redis_client):
        """Test K-line storage failure handling."""
        mock_redis_client.zadd.side_effect = Exception("Redis error")
        
        result = await kline_storage.store_kline(sample_kline)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_store_klines_batch_success(self, kline_storage, mock_redis_client):
        """Test batch K-line storage."""
        klines = [
            KlineData(
                t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
                f=1000, L=1100, o="45000.00", c="45010.00", h="45020.00", l="45000.00",
                v="10.0", n=100, x=True, q="450100.00", V="5.0", Q="225050.00", B="0"
            ),
            KlineData(
                t=1640995260000, T=1640995319999, s="BTCUSDT", i="1m",
                f=1101, L=1200, o="45010.00", c="45020.00", h="45030.00", l="45005.00",
                v="12.0", n=120, x=True, q="540240.00", V="6.0", Q="270120.00", B="0"
            ),
            KlineData(
                t=1640995200000, T=1640995259999, s="ETHUSDT", i="1m",
                f=2000, L=2100, o="3200.00", c="3210.00", h="3220.00", l="3195.00",
                v="15.0", n=150, x=True, q="48150.00", V="7.5", Q="24075.00", B="0"
            )
        ]
        
        result = await kline_storage.store_klines_batch(klines, publish=False)
        
        assert result == 3
        # Should be called twice (once for BTCUSDT, once for ETHUSDT)
        assert mock_redis_client.zadd.call_count == 2
    
    @pytest.mark.asyncio
    async def test_store_klines_batch_empty(self, kline_storage):
        """Test batch storage with empty list."""
        result = await kline_storage.store_klines_batch([])
        assert result == 0
    
    @pytest.mark.asyncio
    async def test_get_klines_success(self, kline_storage, mock_redis_client):
        """Test successful K-line retrieval."""
        # Mock stored data
        storage_data = {
            'ot': 1640995200000,
            'ct': 1640995259999,
            's': 'BTCUSDT',
            'i': '1m',
            'ft': 1234567890,
            'lt': 1234567899,
            'o': '45000.12',
            'c': '45123.45',
            'h': '45200.00',
            'l': '44950.50',
            'v': '123.456789',
            'n': 1500,
            'x': True,
            'q': '5567890.12',
            'bv': '62.123456',
            'bq': '2784567.89'
        }
        
        mock_redis_client.zrangebyscore.return_value = [json.dumps(storage_data)]
        
        result = await kline_storage.get_klines("BTCUSDT", "1m")
        
        assert len(result) == 1
        assert isinstance(result[0], KlineData)
        assert result[0].s == "BTCUSDT"
        assert result[0].i == "1m"
        assert result[0].t == 1640995200000
    
    @pytest.mark.asyncio
    async def test_get_klines_with_time_range(self, kline_storage, mock_redis_client):
        """Test K-line retrieval with time range."""
        mock_redis_client.zrangebyscore.return_value = []
        
        start_time = 1640995200000
        end_time = 1640995500000
        
        await kline_storage.get_klines("BTCUSDT", "1m", start_time, end_time)
        
        # Verify correct parameters were passed
        call_args = mock_redis_client.zrangebyscore.call_args
        assert call_args[0][0] == "kline:BTCUSDT:1m"  # key
        assert call_args[0][1] == float(start_time)    # min_score
        assert call_args[0][2] == float(end_time)      # max_score
    
    @pytest.mark.asyncio
    async def test_get_klines_with_limit(self, kline_storage, mock_redis_client):
        """Test K-line retrieval with limit."""
        mock_redis_client.zrangebyscore.return_value = []
        
        await kline_storage.get_klines("BTCUSDT", "1m", limit=100)
        
        # Verify limit parameters were passed
        call_args = mock_redis_client.zrangebyscore.call_args
        assert call_args[1]['start'] == 0
        assert call_args[1]['num'] == 100
    
    @pytest.mark.asyncio
    async def test_get_klines_invalid_data(self, kline_storage, mock_redis_client):
        """Test K-line retrieval with invalid stored data."""
        mock_redis_client.zrangebyscore.return_value = ["invalid_json", "{}"]
        
        result = await kline_storage.get_klines("BTCUSDT", "1m")
        
        # Should skip invalid data and return empty list
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_get_latest_kline_success(self, kline_storage, mock_redis_client):
        """Test successful latest K-line retrieval."""
        storage_data = {
            'ot': 1640995200000,
            'ct': 1640995259999,
            's': 'BTCUSDT',
            'i': '1m',
            'ft': 1234567890,
            'lt': 1234567899,
            'o': '45000.12',
            'c': '45123.45',
            'h': '45200.00',
            'l': '44950.50',
            'v': '123.456789',
            'n': 1500,
            'x': True,
            'q': '5567890.12',
            'bv': '62.123456',
            'bq': '2784567.89'
        }
        
        mock_redis_client.get.return_value = json.dumps(storage_data)
        
        result = await kline_storage.get_latest_kline("BTCUSDT", "1m")
        
        assert result is not None
        assert isinstance(result, KlineData)
        assert result.s == "BTCUSDT"
        assert result.i == "1m"
    
    @pytest.mark.asyncio
    async def test_get_latest_kline_not_found(self, kline_storage, mock_redis_client):
        """Test latest K-line retrieval when not found."""
        mock_redis_client.get.return_value = None
        
        result = await kline_storage.get_latest_kline("BTCUSDT", "1m")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_kline_count(self, kline_storage, mock_redis_client):
        """Test K-line count retrieval."""
        mock_redis_client.zcard.return_value = 1500
        
        result = await kline_storage.get_kline_count("BTCUSDT", "1m")
        
        assert result == 1500
        mock_redis_client.zcard.assert_called_once_with("kline:BTCUSDT:1m")
    
    @pytest.mark.asyncio
    async def test_delete_klines_all(self, kline_storage, mock_redis_client):
        """Test deleting all K-lines for symbol/interval."""
        mock_redis_client.zcard.return_value = 1000
        
        result = await kline_storage.delete_klines("BTCUSDT", "1m")
        
        assert result == 1000
        mock_redis_client.delete.assert_called()
    
    @pytest.mark.asyncio
    async def test_delete_klines_time_range(self, kline_storage, mock_redis_client):
        """Test deleting K-lines within time range."""
        mock_redis_client.zrangebyscore.return_value = ["data1", "data2", "data3"]
        
        start_time = 1640995200000
        end_time = 1640995500000
        
        result = await kline_storage.delete_klines("BTCUSDT", "1m", start_time, end_time)
        
        assert result == 3
        mock_redis_client.zremrangebyscore.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data(self, kline_storage, mock_redis_client):
        """Test cleanup of old data when limit exceeded."""
        # Simulate exceeding the limit
        mock_redis_client.zcard.return_value = 12000  # > max_klines_per_symbol (10000)
        
        await kline_storage._cleanup_old_data("kline:BTCUSDT:1m")
        
        # Should call zremrangebyrank to remove excess
        mock_redis_client.zremrangebyrank.assert_called_once()
        call_args = mock_redis_client.zremrangebyrank.call_args
        assert call_args[0][0] == "kline:BTCUSDT:1m"
        assert call_args[0][1] == 0  # start
        assert call_args[0][2] == 1999  # end (excess_count - 1)
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data_no_cleanup_needed(self, kline_storage, mock_redis_client):
        """Test cleanup when no cleanup is needed."""
        mock_redis_client.zcard.return_value = 5000  # < max_klines_per_symbol
        
        await kline_storage._cleanup_old_data("kline:BTCUSDT:1m")
        
        # Should not call zremrangebyrank
        mock_redis_client.zremrangebyrank.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_publish_kline_update(self, kline_storage, sample_kline, mock_redis_client):
        """Test publishing K-line update notification."""
        await kline_storage._publish_kline_update(sample_kline)
        
        # Should publish to both specific and global channels
        assert mock_redis_client.publish.call_count == 2
        
        # Check the channels
        calls = mock_redis_client.publish.call_args_list
        channels = [call[0][0] for call in calls]
        
        assert "kline_updates:BTCUSDT:1m" in channels
        assert "kline_updates:*" in channels
    
    @pytest.mark.asyncio
    async def test_get_symbols_with_data(self, kline_storage, mock_redis_client):
        """Test getting symbols with data."""
        mock_redis_client.keys.return_value = [
            "kline:BTCUSDT:1m",
            "kline:BTCUSDT:5m",
            "kline:ETHUSDT:1m",
            "kline:ADAUSDT:1h"
        ]
        
        result = await kline_storage.get_symbols_with_data()
        
        assert result == {"BTCUSDT", "ETHUSDT", "ADAUSDT"}
    
    @pytest.mark.asyncio
    async def test_get_intervals_for_symbol(self, kline_storage, mock_redis_client):
        """Test getting intervals for specific symbol."""
        mock_redis_client.keys.return_value = [
            "kline:BTCUSDT:1m",
            "kline:BTCUSDT:5m",
            "kline:BTCUSDT:1h",
            "kline:BTCUSDT:1d"
        ]
        
        result = await kline_storage.get_intervals_for_symbol("BTCUSDT")
        
        assert result == {"1m", "5m", "1h", "1d"}
        mock_redis_client.keys.assert_called_once_with("kline:BTCUSDT:*")
    
    @pytest.mark.asyncio
    async def test_get_storage_stats(self, kline_storage, mock_redis_client):
        """Test getting storage statistics."""
        mock_redis_client.keys.return_value = [
            "kline:BTCUSDT:1m",
            "kline:BTCUSDT:5m",
            "kline:ETHUSDT:1m"
        ]
        
        # Mock zcard to return different counts for each key
        def mock_zcard(key):
            if key == "kline:BTCUSDT:1m":
                return 1000
            elif key == "kline:BTCUSDT:5m":
                return 200
            elif key == "kline:ETHUSDT:1m":
                return 800
            return 0
        
        mock_redis_client.zcard.side_effect = mock_zcard
        
        result = await kline_storage.get_storage_stats()
        
        assert result['total_symbols'] == 2  # BTCUSDT, ETHUSDT
        assert result['total_intervals'] == 3  # 1m, 5m for BTC + 1m for ETH
        assert result['total_klines'] == 2000  # 1000 + 200 + 800
        assert 'BTCUSDT' in result['symbol_stats']
        assert 'ETHUSDT' in result['symbol_stats']
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_data(self, kline_storage, mock_redis_client):
        """Test cleanup of expired data."""
        mock_redis_client.keys.return_value = ["kline:BTCUSDT:1m", "kline:ETHUSDT:1m"]
        
        # Mock zcard to return counts before and after cleanup
        def mock_zcard_side_effect(key):
            # Simulate reduction after cleanup
            if hasattr(mock_zcard_side_effect, 'call_count'):
                mock_zcard_side_effect.call_count += 1
            else:
                mock_zcard_side_effect.call_count = 1
            
            # Return different values for before/after cleanup
            if mock_zcard_side_effect.call_count % 2 == 1:
                return 1000  # before cleanup
            else:
                return 800   # after cleanup (200 deleted)
        
        mock_redis_client.zcard.side_effect = mock_zcard_side_effect
        
        result = await kline_storage.cleanup_expired_data(days_to_keep=7)
        
        # Should delete old data from both keys: (1000-800) + (1000-800) = 400
        assert result == 400
        
        # Should call zremrangebyscore for each key
        assert mock_redis_client.zremrangebyscore.call_count == 2
    
    @pytest.mark.asyncio
    async def test_subscribe_kline_updates_specific(self, kline_storage):
        """Test subscribing to specific symbol/interval updates."""
        # Test channel name generation
        channel = kline_storage._get_kline_channel("BTCUSDT", "1m")
        assert channel == "kline_updates:BTCUSDT:1m"
        
        # Test the method exists and can be called (actual iteration testing would be integration test)
        subscription = kline_storage.subscribe_kline_updates("BTCUSDT", "1m")
        assert hasattr(subscription, '__aiter__')
    
    @pytest.mark.asyncio
    async def test_subscribe_kline_updates_all(self, kline_storage):
        """Test subscribing to all K-line updates."""
        # Test global channel constant
        assert kline_storage.global_channel == "kline_updates:*"
        
        # Test the method exists and can be called for all updates
        subscription = kline_storage.subscribe_kline_updates()
        assert hasattr(subscription, '__aiter__')
    
    @pytest.mark.asyncio
    async def test_error_handling(self, kline_storage, mock_redis_client):
        """Test error handling in various operations."""
        mock_redis_client.keys.side_effect = Exception("Redis error")
        
        # Test that errors are handled gracefully
        symbols = await kline_storage.get_symbols_with_data()
        assert symbols == set()
        
        intervals = await kline_storage.get_intervals_for_symbol("BTCUSDT")
        assert intervals == set()
        
        stats = await kline_storage.get_storage_stats()
        assert stats == {}
        
        count = await kline_storage.get_kline_count("BTCUSDT", "1m")
        assert count == 0
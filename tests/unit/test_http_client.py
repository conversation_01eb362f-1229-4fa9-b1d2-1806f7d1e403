"""
Unit tests for HTTP client.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import time

import httpx

from kline_processor.clients.http_client import H<PERSON><PERSON>lient, HTTPClientError, RateLimitError


@pytest.fixture
def http_client():
    """Create HTTPClient instance for testing."""
    return HTTPClient(base_url="https://api.example.com", timeout=10.0)


@pytest.fixture
def mock_httpx_client():
    """Create mock httpx.AsyncClient."""
    mock = AsyncMock(spec=httpx.AsyncClient)
    mock.aclose = AsyncMock()
    return mock


@pytest.fixture
def mock_response():
    """Create mock HTTP response."""
    response = MagicMock()
    response.status_code = 200
    response.text = "OK"
    response.json.return_value = {"status": "success"}
    response.headers = {}
    return response


class TestHTTPClient:
    """Test cases for HTTPClient class."""
    
    def test_initialization(self, http_client):
        """Test HTTPClient initialization."""
        assert http_client.base_url == "https://api.example.com"
        assert http_client.timeout == 10.0
        assert http_client.max_retries == 3
        assert http_client.backoff_factor == 0.5
        assert http_client._min_request_interval == 0.1
    
    def test_set_rate_limit(self, http_client):
        """Test setting rate limit."""
        http_client.set_rate_limit(5.0)
        assert http_client._min_request_interval == 0.2  # 1/5
        
        http_client.set_rate_limit(0)
        assert http_client._min_request_interval == 0
    
    @pytest.mark.asyncio
    async def test_context_manager(self, http_client):
        """Test using HTTPClient as async context manager."""
        with patch.object(http_client, 'close') as mock_close:
            async with http_client as client:
                assert client is http_client
            
            mock_close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, http_client):
        """Test rate limiting enforcement."""
        http_client.set_rate_limit(100.0)  # 100 requests per second (0.01s interval)
        
        start_time = time.time()
        
        # Make two requests
        await http_client._enforce_rate_limit()
        await http_client._enforce_rate_limit()
        
        elapsed = time.time() - start_time
        
        # Should take at least 0.01 seconds due to rate limiting
        assert elapsed >= 0.009  # Allow for small timing variations
    
    @pytest.mark.asyncio
    async def test_successful_request(self, http_client, mock_response):
        """Test successful HTTP request."""
        with patch.object(http_client, '_client') as mock_client:
            mock_client.request = AsyncMock(return_value=mock_response)
            
            response = await http_client._make_request('GET', '/test')
            
            assert response == mock_response
            assert http_client._successful_requests == 1
            assert http_client._total_requests == 1
            mock_client.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rate_limit_error(self, http_client):
        """Test handling of rate limit errors."""
        rate_limit_response = MagicMock()
        rate_limit_response.status_code = 429
        rate_limit_response.headers = {'Retry-After': '1'}  # Reduce retry time for testing
        rate_limit_response.text = "Rate limit exceeded"
        
        with patch.object(http_client, '_client') as mock_client:
            mock_client.request = AsyncMock(return_value=rate_limit_response)
            
            with patch('asyncio.sleep') as mock_sleep:  # Mock sleep to speed up test
                with pytest.raises(RateLimitError) as exc_info:
                    await http_client._make_request('GET', '/test')
            
            assert exc_info.value.retry_after == 1
            assert http_client._rate_limited_requests == 4  # initial + max_retries attempts
            assert mock_sleep.call_count == 3  # Should sleep 3 times for retries
    
    @pytest.mark.asyncio
    async def test_server_error_retry(self, http_client, mock_response):
        """Test retry on server errors."""
        server_error_response = MagicMock()
        server_error_response.status_code = 500
        server_error_response.text = "Internal server error"
        
        with patch.object(http_client, '_client') as mock_client:
            # First call returns 500, second call returns 200
            mock_client.request = AsyncMock(side_effect=[server_error_response, mock_response])
            
            with patch('asyncio.sleep') as mock_sleep:
                response = await http_client._make_request('GET', '/test')
            
            assert response == mock_response
            assert mock_client.request.call_count == 2
            mock_sleep.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_client_error_no_retry(self, http_client):
        """Test no retry on client errors."""
        client_error_response = MagicMock()
        client_error_response.status_code = 400
        client_error_response.text = "Bad request"
        
        with patch.object(http_client, '_client') as mock_client:
            mock_client.request = AsyncMock(return_value=client_error_response)
            
            with pytest.raises(HTTPClientError) as exc_info:
                await http_client._make_request('GET', '/test')
            
            assert "HTTP 400" in str(exc_info.value)
            assert mock_client.request.call_count == 1  # No retry
    
    @pytest.mark.asyncio
    async def test_request_exception_retry(self, http_client, mock_response):
        """Test retry on request exceptions."""
        with patch.object(http_client, '_client') as mock_client:
            # First call raises exception, second call succeeds
            mock_client.request = AsyncMock(side_effect=[httpx.RequestError("Connection failed"), mock_response])
            
            with patch('asyncio.sleep') as mock_sleep:
                response = await http_client._make_request('GET', '/test')
            
            assert response == mock_response
            assert mock_client.request.call_count == 2
            mock_sleep.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_max_retries_exceeded(self, http_client):
        """Test behavior when max retries are exceeded."""
        with patch.object(http_client, '_client') as mock_client:
            mock_client.request = AsyncMock(side_effect=httpx.RequestError("Connection failed"))
            
            with patch('asyncio.sleep') as mock_sleep:
                with pytest.raises(HTTPClientError) as exc_info:
                    await http_client._make_request('GET', '/test')
            
            assert "failed after 3 retries" in str(exc_info.value)
            assert mock_client.request.call_count == 4  # Initial + 3 retries
            assert mock_sleep.call_count == 3
    
    def test_get_retry_after_header(self, http_client):
        """Test extracting retry-after from headers."""
        response = MagicMock()
        response.headers = {'Retry-After': '120'}
        
        retry_after = http_client._get_retry_after(response)
        assert retry_after == 120
    
    def test_get_retry_after_rate_limit_reset(self, http_client):
        """Test extracting retry-after from rate limit reset header."""
        future_timestamp = int(time.time()) + 180
        response = MagicMock()
        response.headers = {'X-RateLimit-Reset': str(future_timestamp)}
        
        retry_after = http_client._get_retry_after(response)
        assert retry_after >= 179  # Allow for execution time
    
    def test_get_retry_after_default(self, http_client):
        """Test default retry-after value."""
        response = MagicMock()
        response.headers = {}
        
        retry_after = http_client._get_retry_after(response)
        assert retry_after == 60
    
    @pytest.mark.asyncio
    async def test_get_method(self, http_client, mock_response):
        """Test GET method."""
        with patch.object(http_client, '_make_request') as mock_request:
            mock_request.return_value = mock_response
            
            response = await http_client.get('/test', params={'key': 'value'})
            
            assert response == mock_response
            mock_request.assert_called_once_with('GET', '/test', params={'key': 'value'}, headers=None, auth=None)
    
    @pytest.mark.asyncio
    async def test_post_method(self, http_client, mock_response):
        """Test POST method."""
        with patch.object(http_client, '_make_request') as mock_request:
            mock_request.return_value = mock_response
            
            response = await http_client.post('/test', json_data={'data': 'value'})
            
            assert response == mock_response
            mock_request.assert_called_once_with('POST', '/test', params=None, json_data={'data': 'value'}, 
                                               data=None, headers=None, auth=None)
    
    @pytest.mark.asyncio
    async def test_put_method(self, http_client, mock_response):
        """Test PUT method."""
        with patch.object(http_client, '_make_request') as mock_request:
            mock_request.return_value = mock_response
            
            response = await http_client.put('/test', data='raw data')
            
            assert response == mock_response
            mock_request.assert_called_once_with('PUT', '/test', params=None, json_data=None,
                                               data='raw data', headers=None, auth=None)
    
    @pytest.mark.asyncio
    async def test_delete_method(self, http_client, mock_response):
        """Test DELETE method."""
        with patch.object(http_client, '_make_request') as mock_request:
            mock_request.return_value = mock_response
            
            response = await http_client.delete('/test')
            
            assert response == mock_response
            mock_request.assert_called_once_with('DELETE', '/test', params=None, headers=None, auth=None)
    
    def test_get_stats(self, http_client):
        """Test getting client statistics."""
        http_client._total_requests = 100
        http_client._successful_requests = 95
        http_client._failed_requests = 5
        http_client._rate_limited_requests = 2
        
        stats = http_client.get_stats()
        
        assert stats['total_requests'] == 100
        assert stats['successful_requests'] == 95
        assert stats['failed_requests'] == 5
        assert stats['rate_limited_requests'] == 2
        assert stats['success_rate'] == 95.0
        assert stats['base_url'] == "https://api.example.com"
        assert stats['timeout'] == 10.0
        assert stats['max_retries'] == 3
    
    @pytest.mark.asyncio
    async def test_close(self, http_client):
        """Test closing HTTP client."""
        with patch.object(http_client._client, 'aclose') as mock_close:
            await http_client.close()
            mock_close.assert_called_once()
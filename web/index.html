<!DOCTYPE HTML>
<html>
<head>
    <title>Simple K-Line - New Connection Per Subscription</title>
    <!-- Fix for iOS Safari zooming bug -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0">
    <script type="text/javascript" src="charting_library/charting_library.standalone.js"></script>
    
    <!-- Load simple components -->
    <script type="text/javascript" src="simple-websocket-manager.js"></script>
    <script type="text/javascript" src="simple-kline-datafeed.js"></script>
    
    <script type="text/javascript">
    // Global configuration
    let appConfig = {
        symbols: ['BTCUSDT', 'ETHUSDT'],  // Default fallback
        intervals: [
            { value: '1', label: '1M', api_interval: '1m' },
            { value: '3', label: '3M', api_interval: '3m' },
            { value: '5', label: '5M', api_interval: '5m' },
            { value: '15', label: '15M', api_interval: '15m' },
            { value: '30', label: '30M', api_interval: '30m' },
            { value: '60', label: '1H', api_interval: '1h' }
        ]  // Default fallback
    };
    
    // Global references for debugging
    let datafeed = null;
    let widget = null;
    
    // Load configuration from API
    async function loadConfiguration() {
        try {
            const response = await fetch('/api/v1/config');
            if (response.ok) {
                const config = await response.json();
                appConfig = config;
                console.log('Loaded configuration:', appConfig);
                return true;
            }
        } catch (error) {
            console.error('Failed to load configuration:', error);
        }
        return false;
    }

    async function initOnReady() {
        // Load configuration first
        await loadConfiguration();
        
        // Create WebSocket URL
        const wsUrl = `ws://${window.location.host}/ws/v1/klines`;
        
        // Initialize simple datafeed
        datafeed = new SimpleKlineDatafeed(window.location.origin, wsUrl);
        
        // Initialize WebSocket manager with callback
        datafeed.initWebSocket((key, bar) => {
            datafeed.onRealtimeUpdate(key, bar);
        });
        
        // Use first symbol from config or default
        const defaultSymbol = appConfig.symbols.length > 0 ? appConfig.symbols[0] : 'BTCUSDT';
        
        // Use a default interval from config
        const defaultInterval = appConfig.intervals.find(i => i.api_interval === '15m')?.value || 
                               appConfig.intervals[0]?.value || '15';
        
        // Create TradingView widget
        widget = window.tvWidget = new TradingView.widget({
            fullscreen: true,
            symbol: defaultSymbol,
            interval: defaultInterval,
            container: "tv_chart_container",
            datafeed: datafeed,
            library_path: "charting_library/",
            locale: "en",
            disabled_features: ["use_localstorage_for_settings"],
            enabled_features: ["study_templates"],
            charts_storage_url: 'http://saveload.tradingview.com',
            charts_storage_api_version: "1.1",
            client_id: 'tradingview.com',
            user_id: 'public_user_id',
            theme: "Dark",
            timezone: "Etc/UTC",
            toolbar_bg: '#1e222d',
            loading_screen: { backgroundColor: "#1e222d" },
            overrides: {
                "mainSeriesProperties.candleStyle.upColor": "#26a69a",
                "mainSeriesProperties.candleStyle.downColor": "#ef5350",
                "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350"
            },
            debug: true  // Enable debug mode for better logging
        });
        
        // Add debug panel
        createDebugPanel();
        
        window.frames[0].focus();
    }

    // Create debug panel for monitoring WebSocket state
    function createDebugPanel() {
        // const debugPanel = document.createElement('div');
        // debugPanel.id = 'debug-panel';
        // debugPanel.innerHTML = `
        //     <h3>Simple WebSocket Debug Panel</h3>
        //     <button onclick="toggleDebugPanel()">Toggle</button>
        //     <button onclick="refreshDebugInfo()">Refresh</button>
        //     <button onclick="clearDebugLog()">Clear Log</button>
        //     <button onclick="testSwitching()">Test Switch</button>
        //     <button onclick="showWsState()">Show State</button>
        //     <div id="debug-content" style="display: block;">
        //         <div id="debug-status"></div>
        //         <div id="debug-log"></div>
        //     </div>
        // `;
        // document.body.appendChild(debugPanel);
        
        // // Start periodic debug info updates
        // setInterval(refreshDebugInfo, 1000);
    }
    
    function toggleDebugPanel() {
        const content = document.getElementById('debug-content');
        content.style.display = content.style.display === 'none' ? 'block' : 'none';
    }
    
    function refreshDebugInfo() {
        if (!datafeed) return;
        
        const debugInfo = datafeed.getDebugInfo();
        const statusDiv = document.getElementById('debug-status');
        
        statusDiv.innerHTML = `
            <h4>Status</h4>
            <p>Current Subscription: ${debugInfo.currentSubscriptionKey || 'None'}</p>
            <p>TradingView Subscribers: ${debugInfo.subscriberCount}</p>
            
            <h4>WebSocket Manager</h4>
            ${debugInfo.wsManagerDebug ? `
                <p>Connected: ${debugInfo.wsManagerDebug.connected ? '✅' : '❌'}</p>
                <p>Ready State: ${debugInfo.wsManagerDebug.readyState}</p>
                <p>Current: ${debugInfo.wsManagerDebug.currentSubscription || 'None'}</p>
                <p>Reconnect Scheduled: ${debugInfo.wsManagerDebug.reconnectScheduled ? 'Yes' : 'No'}</p>
            ` : '<p>Not initialized</p>'}
            
            <h4>TradingView Subscribers</h4>
            <ul>
                ${debugInfo.subscribers.map(sub => `<li>UID: ${sub.uid} → ${sub.key}</li>`).join('')}
            </ul>
        `;
    }
    
    function clearDebugLog() {
        const logDiv = document.getElementById('debug-log');
        logDiv.innerHTML = '';
    }
    
    function testSwitching() {
        if (widget) {
            console.log('===== TEST SWITCHING START =====');
            
            // Get available symbols and intervals from config
            const symbols = appConfig.symbols;
            const intervals = appConfig.intervals;
            
            // Create a random sequence of switches
            let switches = [
                { symbol: 'BTCUSDT', interval: '15' },
                { symbol: 'BTCUSDT', interval: '1' },
                { symbol: 'ETHUSDT', interval: '5' },
                { symbol: 'BTCUSDT', interval: '3' }
            ];
            
            // Execute switches with delays
            switches.forEach((sw, index) => {
                setTimeout(() => {
                    console.log(`Switching to ${sw.symbol} ${sw.interval}m`);
                    widget.setSymbol(sw.symbol, sw.interval);
                }, index * 2000);
            });
        }
    }
    
    function showWsState() {
        if (datafeed) {
            const debugInfo = datafeed.getDebugInfo();
            console.log('===== Current State =====');
            console.log(JSON.stringify(debugInfo, null, 2));
            console.log('========================');
        }
    }
    
    // Override console.log to capture WebSocket logs
    const originalLog = console.log;
    console.log = function(...args) {
        originalLog.apply(console, args);
        
        // Add to debug log if it contains WebSocket-related info
        const message = args.join(' ');
        if (message.includes('[Simple') || message.includes('Switching to')) {
            const logDiv = document.getElementById('debug-log');
            if (logDiv) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                // Color code based on message type
                if (message.includes('Creating new connection')) {
                    logEntry.style.color = '#00ff00';
                    logEntry.style.fontWeight = 'bold';
                } else if (message.includes('Closing connection')) {
                    logEntry.style.color = '#ff0000';
                    logEntry.style.fontWeight = 'bold';
                } else if (message.includes('Subscribe request')) {
                    logEntry.style.color = '#ffcc00';
                } else if (message.includes('Sent subscribe')) {
                    logEntry.style.color = '#66ff66';
                } else if (message.includes('Delivered update')) {
                    logEntry.style.color = '#cccccc';
                }
                
                logEntry.textContent = `[${timestamp}] ${message}`;
                logDiv.appendChild(logEntry);
                
                // Keep only last 50 entries
                while (logDiv.children.length > 50) {
                    logDiv.removeChild(logDiv.firstChild);
                }
                
                // Auto-scroll to bottom
                logDiv.scrollTop = logDiv.scrollHeight;
            }
        }
    };

    window.addEventListener('DOMContentLoaded', initOnReady, false);
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (datafeed) {
            datafeed.destroy();
        }
    });
    </script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1e222d;
            font-family: Arial, sans-serif;
        }
        
        #tv_chart_container {
            height: 100vh;
        }
        
        #debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 600px;
            background-color: rgba(30, 34, 45, 0.95);
            border: 1px solid #363c4e;
            border-radius: 5px;
            padding: 10px;
            color: #d1d4dc;
            font-size: 12px;
            z-index: 1000;
            overflow: hidden;
        }
        
        #debug-panel h3, #debug-panel h4 {
            margin: 5px 0;
            color: #fff;
        }
        
        #debug-panel button {
            margin: 2px;
            padding: 5px 10px;
            background-color: #363c4e;
            color: #d1d4dc;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        #debug-panel button:hover {
            background-color: #434651;
        }
        
        #debug-content {
            margin-top: 10px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        #debug-status {
            border-bottom: 1px solid #363c4e;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        #debug-log {
            max-height: 200px;
            overflow-y: auto;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-entry {
            padding: 2px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            white-space: pre-wrap;
            word-break: break-all;
            font-family: monospace;
            font-size: 11px;
        }
        
        #debug-status ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        #debug-status p {
            margin: 3px 0;
        }
    </style>
</head>

<body>
    <div id="tv_chart_container"></div>
</body>

</html>
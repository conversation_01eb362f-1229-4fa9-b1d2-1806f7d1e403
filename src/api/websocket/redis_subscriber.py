"""
Redis pub/sub subscriber for broadcasting K-line updates to WebSocket clients.
"""

import asyncio
import json
from typing import Op<PERSON>

from kline_processor.storage.redis_client import RedisClient
from kline_processor.config.settings import settings
from kline_processor.utils.logger import get_logger
from .connection_manager import ConnectionManager

logger = get_logger("websocket.redis_subscriber")

# Global connection manager instance
manager: Optional[ConnectionManager] = None


def set_connection_manager(connection_manager: ConnectionManager):
    """Set the global connection manager instance."""
    global manager
    manager = connection_manager


async def start_redis_subscriber(redis_client: RedisClient):
    """
    Start Redis pub/sub subscriber for K-line updates.
    
    This function subscribes to all K-line update channels and
    broadcasts updates to WebSocket clients.
    """
    global manager
    
    if not manager:
        logger.error("Connection manager not set")
        return
    
    logger.info("Starting Redis subscriber for K-line updates")
    
    pubsub = None
    redis_conn = None
    try:
        # Create a dedicated Redis connection for pubsub (without timeout context)
        import redis.asyncio as redis
        redis_conn = redis.Redis(
            host=settings.redis_host,
            port=settings.redis_port,
            db=settings.redis_db,
            password=settings.redis_password if settings.redis_password else None,
            decode_responses=False,  # We'll handle decoding ourselves
            socket_connect_timeout=5,
            socket_timeout=None,  # No timeout for pubsub
            retry_on_timeout=False
        )
        
        pubsub = redis_conn.pubsub()
        
        # Subscribe to all K-line updates
        await pubsub.psubscribe("kline_updates:*")
        logger.info("Subscribed to kline_updates:* pattern")
        
        # Listen for messages
        while True:
            try:
                message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                if message and message.get('type') in ('pmessage', 'message'):
                    try:
                        channel = message.get('channel')
                        if isinstance(channel, bytes):
                            channel = channel.decode('utf-8')
                        parts = str(channel).split(':')
                        if len(parts) == 3:
                            symbol = parts[1]
                            interval = parts[2]
                            data = message.get('data')
                            if isinstance(data, (bytes, bytearray)):
                                data = data.decode('utf-8')
                            redis_message = json.loads(data)
                            
                            # Extract K-line data from the nested 'data' field
                            if 'data' in redis_message:
                                kline_data = redis_message['data']
                                # Add symbol and interval to the K-line data if not present
                                if 's' not in kline_data:
                                    kline_data['s'] = symbol
                                if 'i' not in kline_data:
                                    kline_data['i'] = interval
                                await manager.broadcast_kline(symbol, interval, kline_data)
                            else:
                                logger.warning(f"No 'data' field in Redis message for {symbol}@{interval}")
                    except Exception as e:
                        logger.error(f"Error processing Redis message: {e}")
                        
                # Small sleep to prevent busy waiting
                await asyncio.sleep(0.01)
                
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"Error getting message: {e}")
                await asyncio.sleep(1.0)  # Wait before retrying
                
    except asyncio.CancelledError:
        logger.info("Redis subscriber cancelled")
        raise
    except Exception as e:
        logger.error(f"Redis subscriber error: {e}")
    finally:
        try:
            if pubsub is not None:
                await pubsub.unsubscribe()
                await pubsub.aclose()
            if redis_conn is not None:
                await redis_conn.aclose()
        except Exception:
            pass
        logger.info("Redis subscriber stopped")
"""
K-line data models for API requests and responses.

This module defines Pydantic models that match the required external format
while maintaining compatibility with the internal storage format.
"""

from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Literal
from datetime import datetime


class KlineQueryParams(BaseModel):
    """Query parameters for K-line data retrieval."""
    
    symbol: str = Field(
        ..., 
        description="Trading pair symbol",
        examples=["BTCUSDT", "ETHUSDT"]
    )
    interval: Literal["1m", "3m", "5m", "15m", "30m", "1h", "4h", "1d"] = Field(
        ..., 
        description="K-line interval/timeframe"
    )
    start_time: Optional[int] = Field(
        None, 
        description="Start timestamp in milliseconds",
        ge=0
    )
    end_time: Optional[int] = Field(
        None, 
        description="End timestamp in milliseconds",
        ge=0
    )
    limit: int = Field(
        500, 
        description="Maximum number of records to return",
        ge=1,
        le=1000
    )
    
    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v: str) -> str:
        """Ensure symbol is uppercase."""
        return v.upper()
    
    @field_validator('end_time')
    @classmethod
    def validate_time_range(cls, v: Optional[int], info) -> Optional[int]:
        """Ensure end_time is greater than start_time if both are provided."""
        if v is not None and 'start_time' in info.data:
            start_time = info.data['start_time']
            if start_time is not None and v <= start_time:
                raise ValueError('end_time must be greater than start_time')
        return v


class KlineData(BaseModel):
    """
    K-line data response model.
    
    This format matches the storage format which is already aligned
    with the required external API format.
    """
    
    ot: int = Field(..., description="Open time (timestamp in milliseconds)")
    ct: int = Field(..., description="Close time (timestamp in milliseconds)")
    s: str = Field(..., description="Symbol")
    i: str = Field(..., description="Interval")
    ft: int = Field(..., description="First trade ID")
    lt: int = Field(..., description="Last trade ID")
    o: str = Field(..., description="Open price")
    c: str = Field(..., description="Close price")
    h: str = Field(..., description="High price")
    l: str = Field(..., description="Low price")
    v: str = Field(..., description="Volume (base asset)")
    n: int = Field(..., description="Number of trades")
    x: bool = Field(..., description="Is K-line closed")
    q: str = Field(..., description="Quote asset volume")
    bv: str = Field(..., description="Taker buy base asset volume")
    bq: str = Field(..., description="Taker buy quote asset volume")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "ot": 1754149500000,
                "ct": 1754150399999,
                "s": "BTCUSDT",
                "i": "15m",
                "ft": 6523455279,
                "lt": 6523456082,
                "o": "112884.40",
                "c": "112841.80",
                "h": "112888.00",
                "l": "112841.70",
                "v": "37.515",
                "n": 804,
                "x": False,
                "q": "4234389.63450",
                "bv": "6.098",
                "bq": "688293.63750"
            }
        }
    }


class KlineResponse(BaseModel):
    """Standard API response wrapper for K-line data."""
    
    success: bool = Field(True, description="Request success status")
    data: List[KlineData] = Field(..., description="Array of K-line data")
    count: int = Field(..., description="Number of records returned")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "data": [KlineData.model_config["json_schema_extra"]["example"]],
                "count": 1
            }
        }
    }
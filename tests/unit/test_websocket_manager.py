"""
Unit tests for WebSocket Manager.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from kline_processor.websocket.websocket_manager import (
    WebSocketManager, ConnectionState, WebSocketError, ConnectionError
)
from kline_processor.websocket.reconnection_handler import ReconnectionStrategy
from kline_processor.models.websocket import WebSocketMessage, StreamType
from kline_processor.models.kline import KlineData


@pytest.fixture
def websocket_manager():
    """Create WebSocketManager for testing."""
    return WebSocketManager(
        base_url="wss://stream.binance.com:9443/ws",
        ping_interval=10.0,
        ping_timeout=5.0,
        max_message_size=1024,
        reconnection_strategy=ReconnectionStrategy.EXPONENTIAL
    )


@pytest.fixture
def mock_websocket():
    """Create mock websocket connection."""
    mock = AsyncMock()
    mock.closed = False
    mock.close = AsyncMock()
    mock.send = AsyncMock()
    mock.recv = AsyncMock()
    mock.ping = AsyncMock()
    return mock


@pytest.fixture
def sample_kline_message():
    """Create sample K-line WebSocket message."""
    return {
        "e": "kline",
        "E": 1640995200000,
        "s": "BTCUSDT",
        "k": {
            "t": 1640995200000,
            "T": 1640995259999,
            "s": "BTCUSDT",
            "i": "1m",
            "f": 100,
            "L": 200,
            "o": "45000.12",
            "c": "45123.45",
            "h": "45200.00",
            "l": "44950.50",
            "v": "123.456789",
            "n": 1500,
            "x": True,
            "q": "5567890.12",
            "V": "62.123456",
            "Q": "2784567.89",
            "B": "0"
        }
    }


class TestWebSocketManager:
    """Test cases for WebSocketManager."""
    
    def test_initialization(self, websocket_manager):
        """Test WebSocket manager initialization."""
        assert websocket_manager.base_url == "wss://stream.binance.com:9443/ws"
        assert websocket_manager.ping_interval == 10.0
        assert websocket_manager.ping_timeout == 5.0
        assert websocket_manager.max_message_size == 1024
        assert websocket_manager.connection_state == ConnectionState.DISCONNECTED
        assert not websocket_manager.is_connected
    
    @pytest.mark.asyncio
    async def test_context_manager(self, websocket_manager):
        """Test using WebSocketManager as async context manager."""
        with patch.object(websocket_manager, 'close') as mock_close:
            async with websocket_manager as manager:
                assert manager is websocket_manager
            
            mock_close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connect_success(self, websocket_manager, mock_websocket):
        """Test successful WebSocket connection."""
        streams = ["btcusdt@kline_1m", "ethusdt@kline_1m"]
        
        # Use AsyncMock for the patched connect function
        with patch('websockets.connect', new_callable=AsyncMock, return_value=mock_websocket):
            with patch.object(websocket_manager, '_start_background_tasks') as mock_start_tasks:
                result = await websocket_manager.connect(streams)
        
        assert result is True
        assert websocket_manager.connection_state == ConnectionState.CONNECTED
        assert websocket_manager.is_connected
        assert websocket_manager._subscribed_streams == set(streams)
        mock_start_tasks.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connect_failure(self, websocket_manager):
        """Test WebSocket connection failure."""
        streams = ["btcusdt@kline_1m"]
        
        with patch('websockets.connect', side_effect=Exception("Connection failed")):
            with patch.object(websocket_manager, '_schedule_reconnection') as mock_schedule:
                result = await websocket_manager.connect(streams)
        
        assert result is False
        assert websocket_manager.connection_state == ConnectionState.DISCONNECTED
        assert not websocket_manager.is_connected
        mock_schedule.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connect_already_connected(self, websocket_manager, mock_websocket):
        """Test connecting when already connected."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        
        result = await websocket_manager.connect(["btcusdt@kline_1m"])
        
        assert result is True  # Should return True without attempting reconnection
    
    @pytest.mark.asyncio
    async def test_disconnect(self, websocket_manager, mock_websocket):
        """Test WebSocket disconnection."""
        # Set up connected state
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        websocket_manager._subscribed_streams.add("btcusdt@kline_1m")
        
        with patch.object(websocket_manager, '_stop_background_tasks') as mock_stop_tasks:
            await websocket_manager.disconnect()
        
        assert websocket_manager.connection_state == ConnectionState.DISCONNECTED
        assert websocket_manager._websocket is None
        assert len(websocket_manager._subscribed_streams) == 0
        mock_websocket.close.assert_called_once()
        mock_stop_tasks.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close(self, websocket_manager):
        """Test closing WebSocket manager."""
        with patch.object(websocket_manager, 'disconnect') as mock_disconnect:
            await websocket_manager.close()
        
        assert websocket_manager.connection_state == ConnectionState.CLOSED
        mock_disconnect.assert_called_once()
    
    def test_add_message_handler(self, websocket_manager):
        """Test adding message handler."""
        handler = MagicMock()
        
        websocket_manager.add_message_handler(StreamType.KLINE, handler)
        
        assert StreamType.KLINE in websocket_manager._message_handlers
        assert handler in websocket_manager._message_handlers[StreamType.KLINE]
    
    def test_remove_message_handler(self, websocket_manager):
        """Test removing message handler."""
        handler = MagicMock()
        websocket_manager._message_handlers[StreamType.KLINE] = [handler]
        
        websocket_manager.remove_message_handler(StreamType.KLINE, handler)
        
        assert handler not in websocket_manager._message_handlers[StreamType.KLINE]
    
    def test_remove_nonexistent_handler(self, websocket_manager):
        """Test removing non-existent message handler."""
        handler = MagicMock()
        
        # Should not raise exception
        websocket_manager.remove_message_handler(StreamType.KLINE, handler)
    
    @pytest.mark.asyncio
    async def test_send_message_success(self, websocket_manager, mock_websocket):
        """Test successful message sending."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        
        message = {"method": "SUBSCRIBE", "params": ["btcusdt@kline_1m"], "id": 1}
        result = await websocket_manager.send_message(message)
        
        assert result is True
        mock_websocket.send.assert_called_once()
        assert websocket_manager._stats.messages_sent == 1
    
    @pytest.mark.asyncio
    async def test_send_message_not_connected(self, websocket_manager):
        """Test sending message when not connected."""
        message = {"method": "SUBSCRIBE", "params": ["btcusdt@kline_1m"], "id": 1}
        result = await websocket_manager.send_message(message)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_send_message_failure(self, websocket_manager, mock_websocket):
        """Test message sending failure."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        mock_websocket.send.side_effect = Exception("Send failed")
        
        message = {"method": "SUBSCRIBE", "params": ["btcusdt@kline_1m"], "id": 1}
        result = await websocket_manager.send_message(message)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_subscribe_to_streams(self, websocket_manager):
        """Test subscribing to additional streams."""
        websocket_manager._subscribed_streams = {"btcusdt@kline_1m"}
        
        with patch.object(websocket_manager, 'disconnect') as mock_disconnect:
            with patch.object(websocket_manager, 'connect', return_value=True) as mock_connect:
                result = await websocket_manager.subscribe_to_streams(["ethusdt@kline_1m"])
        
        assert result is True
        mock_disconnect.assert_called_once()
        mock_connect.assert_called_once_with(["btcusdt@kline_1m", "ethusdt@kline_1m"])
    
    @pytest.mark.asyncio
    async def test_subscribe_empty_streams(self, websocket_manager):
        """Test subscribing to empty stream list."""
        result = await websocket_manager.subscribe_to_streams([])
        assert result is True
    
    @pytest.mark.asyncio
    async def test_unsubscribe_from_streams(self, websocket_manager):
        """Test unsubscribing from streams."""
        websocket_manager._subscribed_streams = {"btcusdt@kline_1m", "ethusdt@kline_1m"}
        
        with patch.object(websocket_manager, 'disconnect') as mock_disconnect:
            with patch.object(websocket_manager, 'connect', return_value=True) as mock_connect:
                result = await websocket_manager.unsubscribe_from_streams(["ethusdt@kline_1m"])
        
        assert result is True
        mock_disconnect.assert_called_once()
        mock_connect.assert_called_once_with(["btcusdt@kline_1m"])
    
    @pytest.mark.asyncio
    async def test_unsubscribe_all_streams(self, websocket_manager):
        """Test unsubscribing from all streams."""
        websocket_manager._subscribed_streams = {"btcusdt@kline_1m"}
        
        with patch.object(websocket_manager, 'disconnect') as mock_disconnect:
            result = await websocket_manager.unsubscribe_from_streams(["btcusdt@kline_1m"])
        
        assert result is True
        mock_disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_unsubscribe_empty_streams(self, websocket_manager):
        """Test unsubscribing from empty stream list."""
        result = await websocket_manager.unsubscribe_from_streams([])
        assert result is True
    
    @pytest.mark.asyncio
    async def test_start_background_tasks(self, websocket_manager):
        """Test starting background tasks."""
        with patch('asyncio.create_task') as mock_create_task:
            await websocket_manager._start_background_tasks()
        
        assert mock_create_task.call_count == 2  # message processor + heartbeat
        assert websocket_manager._message_processor_task is not None
        assert websocket_manager._heartbeat_task is not None
    
    @pytest.mark.asyncio
    async def test_stop_background_tasks(self, websocket_manager):
        """Test stopping background tasks."""
        # Create simple mock tasks
        mock_task1 = MagicMock()
        mock_task2 = MagicMock()
        
        # Configure the mock tasks
        mock_task1.done.return_value = False
        mock_task2.done.return_value = False
        
        # Configure what happens when we await the task after cancellation
        async def cancelled_task():
            raise asyncio.CancelledError()
        
        mock_task1.__await__ = cancelled_task().__await__
        mock_task2.__await__ = cancelled_task().__await__
        
        websocket_manager._message_processor_task = mock_task1
        websocket_manager._heartbeat_task = mock_task2
        
        await websocket_manager._stop_background_tasks()
        
        mock_task1.cancel.assert_called_once()
        mock_task2.cancel.assert_called_once()
        assert websocket_manager._message_processor_task is None
        assert websocket_manager._heartbeat_task is None
    
    @pytest.mark.asyncio
    async def test_message_processor_success(self, websocket_manager, mock_websocket, sample_kline_message):
        """Test successful message processing."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        
        # Mock receiving one message then stop
        message_json = json.dumps(sample_kline_message)
        mock_websocket.recv.side_effect = [message_json, asyncio.CancelledError()]
        
        # Add message handler
        handler = MagicMock()
        websocket_manager.add_message_handler(StreamType.KLINE, handler)
        
        with patch.object(websocket_manager, '_process_message') as mock_process:
            try:
                await websocket_manager._message_processor()
            except asyncio.CancelledError:
                pass
        
        mock_process.assert_called_once_with(message_json)
        assert websocket_manager._stats.messages_received == 1
    
    @pytest.mark.asyncio
    async def test_message_processor_timeout(self, websocket_manager, mock_websocket):
        """Test message processor timeout."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        
        # Mock timeout then stop
        mock_websocket.recv.side_effect = [asyncio.TimeoutError(), asyncio.CancelledError()]
        
        try:
            await websocket_manager._message_processor()
        except asyncio.CancelledError:
            pass
        
        # Should continue after timeout
        assert websocket_manager._stats.messages_received == 0
    
    @pytest.mark.asyncio
    async def test_process_message_success(self, websocket_manager, sample_kline_message):
        """Test successful message processing."""
        handler = MagicMock()
        websocket_manager.add_message_handler(StreamType.KLINE, handler)
        
        with patch('kline_processor.models.websocket.WebSocketMessage.from_dict') as mock_from_dict:
            mock_message = MagicMock()
            mock_message.get_stream_type.return_value = StreamType.KLINE
            mock_from_dict.return_value = mock_message
            
            message_json = json.dumps(sample_kline_message)
            await websocket_manager._process_message(message_json)
        
        mock_from_dict.assert_called_once()
        handler.assert_called_once_with(mock_message)
    
    @pytest.mark.asyncio
    async def test_process_message_async_handler(self, websocket_manager, sample_kline_message):
        """Test processing message with async handler."""
        async_handler = AsyncMock()
        websocket_manager.add_message_handler(StreamType.KLINE, async_handler)
        
        with patch('kline_processor.models.websocket.WebSocketMessage.from_dict') as mock_from_dict:
            mock_message = MagicMock()
            mock_message.get_stream_type.return_value = StreamType.KLINE
            mock_from_dict.return_value = mock_message
            
            message_json = json.dumps(sample_kline_message)
            await websocket_manager._process_message(message_json)
        
        async_handler.assert_called_once_with(mock_message)
    
    @pytest.mark.asyncio
    async def test_process_message_invalid_json(self, websocket_manager):
        """Test processing invalid JSON message."""
        invalid_json = "invalid json {"
        
        # Should not raise exception
        await websocket_manager._process_message(invalid_json)
    
    @pytest.mark.asyncio
    async def test_process_message_handler_error(self, websocket_manager, sample_kline_message):
        """Test processing message when handler raises error."""
        handler = MagicMock()
        handler.side_effect = Exception("Handler error")
        websocket_manager.add_message_handler(StreamType.KLINE, handler)
        
        with patch('kline_processor.models.websocket.WebSocketMessage.from_dict') as mock_from_dict:
            mock_message = MagicMock()
            mock_message.get_stream_type.return_value = StreamType.KLINE
            mock_from_dict.return_value = mock_message
            
            message_json = json.dumps(sample_kline_message)
            # Should not raise exception
            await websocket_manager._process_message(message_json)
        
        handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_heartbeat_monitor_success(self, websocket_manager, mock_websocket):
        """Test successful heartbeat monitoring."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        
        # Mock ping success then stop
        pong_waiter = AsyncMock()
        mock_websocket.ping.return_value = pong_waiter
        
        # Stop after first ping
        with patch('asyncio.sleep', side_effect=[None, asyncio.CancelledError()]):
            try:
                await websocket_manager._heartbeat_monitor()
            except asyncio.CancelledError:
                pass
        
        mock_websocket.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_heartbeat_monitor_timeout(self, websocket_manager, mock_websocket):
        """Test heartbeat monitoring timeout."""
        websocket_manager._websocket = mock_websocket
        websocket_manager._connection_state = ConnectionState.CONNECTED
        
        # Mock ping timeout
        pong_waiter = AsyncMock()
        mock_websocket.ping.return_value = pong_waiter
        
        with patch('asyncio.wait_for', side_effect=asyncio.TimeoutError()):
            await websocket_manager._heartbeat_monitor()
        
        mock_websocket.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_schedule_reconnection(self, websocket_manager):
        """Test scheduling reconnection."""
        websocket_manager._should_reconnect = True
        
        with patch.object(websocket_manager, '_reconnection_loop') as mock_loop:
            with patch('asyncio.create_task') as mock_create_task:
                await websocket_manager._schedule_reconnection()
        
        assert websocket_manager.connection_state == ConnectionState.RECONNECTING
        mock_create_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_schedule_reconnection_disabled(self, websocket_manager):
        """Test scheduling reconnection when disabled."""
        websocket_manager._should_reconnect = False
        
        with patch('asyncio.create_task') as mock_create_task:
            await websocket_manager._schedule_reconnection()
        
        mock_create_task.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_reconnection_loop_success(self, websocket_manager):
        """Test successful reconnection loop."""
        websocket_manager._should_reconnect = True
        websocket_manager._subscribed_streams = {"btcusdt@kline_1m"}
        
        with patch.object(websocket_manager.reconnection_handler, 'get_next_delay', return_value=0.1):
            with patch.object(websocket_manager, 'connect', return_value=True) as mock_connect:
                with patch('asyncio.sleep'):
                    await websocket_manager._reconnection_loop()
        
        mock_connect.assert_called_once_with(["btcusdt@kline_1m"])
        assert websocket_manager._stats.reconnections == 1
    
    @pytest.mark.asyncio
    async def test_reconnection_loop_failure(self, websocket_manager):
        """Test reconnection loop with connection failure."""
        websocket_manager._should_reconnect = True
        websocket_manager._subscribed_streams = {"btcusdt@kline_1m"}
        
        with patch.object(websocket_manager.reconnection_handler, 'get_next_delay', side_effect=[0.1, asyncio.CancelledError()]):
            with patch.object(websocket_manager, 'connect', return_value=False):
                with patch('asyncio.sleep'):
                    try:
                        await websocket_manager._reconnection_loop()
                    except asyncio.CancelledError:
                        pass
        
        assert websocket_manager._stats.reconnections == 0
    
    def test_get_statistics(self, websocket_manager):
        """Test getting connection statistics."""
        # Set up some test data
        websocket_manager._stats.total_connections = 5
        websocket_manager._stats.successful_connections = 4
        websocket_manager._stats.failed_connections = 1
        websocket_manager._stats.messages_received = 100
        websocket_manager._stats.messages_sent = 50
        websocket_manager._subscribed_streams = {"btcusdt@kline_1m", "ethusdt@kline_1m"}
        
        stats = websocket_manager.get_statistics()
        
        assert stats['connection_state'] == ConnectionState.DISCONNECTED.value
        assert stats['is_connected'] is False
        assert set(stats['subscribed_streams']) == {"btcusdt@kline_1m", "ethusdt@kline_1m"}
        assert stats['total_connections'] == 5
        assert stats['successful_connections'] == 4
        assert stats['failed_connections'] == 1
        assert stats['messages_received'] == 100
        assert stats['messages_sent'] == 50
        assert stats['ping_interval'] == 10.0
        assert stats['ping_timeout'] == 5.0
    
    def test_get_statistics_with_uptime(self, websocket_manager):
        """Test getting statistics with connection uptime."""
        import time
        
        websocket_manager._connection_state = ConnectionState.CONNECTED
        websocket_manager._websocket = MagicMock()
        websocket_manager._websocket.closed = False
        websocket_manager._connection_start_time = time.time() - 10.0  # 10 seconds ago
        
        stats = websocket_manager.get_statistics()
        
        assert stats['is_connected'] is True
        assert stats['connection_uptime_seconds'] >= 9.0  # Should be around 10 seconds
"""
Unit tests for Historical Data Service.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta, timezone

from kline_processor.services.historical_data_service import (
    HistoricalDataService, FetchRequest, FetchResult
)
from kline_processor.clients.exchange_api_client import ExchangeAPIClient, RateLimitError
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.models.kline import KlineData


@pytest.fixture
def mock_exchange_client():
    """Create mock ExchangeAPIClient."""
    mock = AsyncMock(spec=ExchangeAPIClient)
    mock.close = AsyncMock()
    return mock


@pytest.fixture
def mock_storage():
    """Create mock KlineStorage."""
    mock = AsyncMock(spec=KlineStorage)
    return mock


@pytest.fixture
def historical_service(mock_exchange_client, mock_storage):
    """Create HistoricalDataService with mocks."""
    return HistoricalDataService(
        exchange_client=mock_exchange_client,
        storage=mock_storage,
        max_concurrent_requests=2,
        chunk_size=100
    )


@pytest.fixture
def sample_kline():
    """Create sample K-line data."""
    return KlineData(
        t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
        f=0, L=0, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
        v="123.456789", n=1500, x=True, q="5567890.12",
        V="62.123456", Q="2784567.89", B="0"
    )


class TestHistoricalDataService:
    """Test cases for HistoricalDataService."""
    
    def test_initialization(self, historical_service):
        """Test service initialization."""
        assert historical_service.max_concurrent_requests == 2
        assert historical_service.chunk_size == 100
        assert historical_service.gap_tolerance_minutes == 5
        assert historical_service._total_requests == 0
    
    @pytest.mark.asyncio
    async def test_context_manager(self, historical_service):
        """Test using service as async context manager."""
        with patch.object(historical_service, 'close') as mock_close:
            async with historical_service as service:
                assert service is historical_service
            
            mock_close.assert_called_once()
    
    def test_calculate_pagination_params(self, historical_service):
        """Test pagination parameter calculation."""
        # Test 1 hour range with 1-minute interval
        start_time = 1640995200000  # 2022-01-01 00:00:00
        end_time = 1640998800000    # 2022-01-01 01:00:00
        
        params = historical_service.calculate_pagination_params(
            "BTCUSDT", "1m", start_time, end_time
        )
        
        assert len(params) == 1  # Should fit in one chunk
        assert params[0][0] == start_time
        assert params[0][1] == end_time
        assert params[0][2] == 60  # 60 minutes
    
    def test_calculate_pagination_params_large_range(self, historical_service):
        """Test pagination with large time range."""
        # Test 1 day range with 1-minute interval
        start_time = 1640995200000
        end_time = start_time + (24 * 60 * 60 * 1000)  # 24 hours later
        
        params = historical_service.calculate_pagination_params(
            "BTCUSDT", "1m", start_time, end_time
        )
        
        assert len(params) > 1  # Should be split into multiple chunks
        
        # Verify chunks are sequential and non-overlapping
        for i in range(len(params) - 1):
            assert params[i][1] == params[i + 1][0]
    
    def test_calculate_pagination_params_invalid(self, historical_service):
        """Test pagination with invalid parameters."""
        # Test invalid time range
        params = historical_service.calculate_pagination_params(
            "BTCUSDT", "1m", 1640998800000, 1640995200000
        )
        assert params == []
        
        # Test invalid interval - should return empty list when interval is invalid
        params = historical_service.calculate_pagination_params(
            "BTCUSDT", "invalid", 1640995200000, 1640998800000
        )
        assert params == []
    
    @pytest.mark.asyncio
    async def test_fetch_historical_data_success(self, historical_service, sample_kline, mock_exchange_client, mock_storage):
        """Test successful historical data fetch."""
        # Mock exchange client response
        mock_exchange_client.get_klines.return_value = [sample_kline]
        
        # Mock storage operations
        mock_storage.store_klines_batch.return_value = 1
        
        result = await historical_service.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1m",
            start_time=1640995200000,
            end_time=1640998800000,
            store_data=True
        )
        
        assert result.success is True
        assert result.klines_fetched == 1
        assert result.symbol == "BTCUSDT"
        assert result.interval == "1m"
        assert result.error is None
        
        # Verify API was called
        mock_exchange_client.get_klines.assert_called()
        mock_storage.store_klines_batch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_fetch_historical_data_invalid_range(self, historical_service):
        """Test fetch with invalid time range."""
        result = await historical_service.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1m",
            start_time=1640998800000,
            end_time=1640995200000  # End before start
        )
        
        assert result.success is False
        assert result.klines_fetched == 0
        assert "Invalid time range" in result.error
    
    @pytest.mark.asyncio
    async def test_fetch_historical_data_with_rate_limit(self, historical_service, sample_kline, mock_exchange_client, mock_storage):
        """Test fetch with rate limiting."""
        # First call raises rate limit, second succeeds
        rate_limit_error = RateLimitError("Rate limited", retry_after=1)
        mock_exchange_client.get_klines.side_effect = [rate_limit_error, [sample_kline]]
        mock_storage.store_klines_batch.return_value = 1
        
        with patch('asyncio.sleep') as mock_sleep:
            result = await historical_service.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1m",
                start_time=1640995200000,
                end_time=1640995260000,  # Small range
                store_data=True
            )
        
        assert result.success is True
        assert result.klines_fetched == 1
        assert historical_service._rate_limit_hits == 1
        mock_sleep.assert_called_once_with(1)  # Should wait 1 second
    
    @pytest.mark.asyncio
    async def test_fetch_historical_data_exchange_error(self, historical_service, mock_exchange_client):
        """Test fetch with exchange API error."""
        mock_exchange_client.get_klines.side_effect = Exception("API Error")
        
        result = await historical_service.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1m",
            start_time=1640995200000,
            end_time=1640995260000
        )
        
        assert result.success is False
        assert result.klines_fetched == 0
        # The error will be wrapped as chunk failure since the exchange API failed
        assert "chunks failed" in result.error
    
    @pytest.mark.asyncio
    async def test_fetch_multiple_symbols(self, historical_service, sample_kline, mock_exchange_client, mock_storage):
        """Test fetching multiple symbols."""
        # Mock successful responses
        mock_exchange_client.get_klines.return_value = [sample_kline]
        mock_storage.store_klines_batch.return_value = 1
        
        requests = [
            FetchRequest(
                symbol="BTCUSDT",
                interval="1m",
                start_time=1640995200000,
                end_time=1640995260000,
                priority=2
            ),
            FetchRequest(
                symbol="ETHUSDT",
                interval="1m", 
                start_time=1640995200000,
                end_time=1640995260000,
                priority=1
            )
        ]
        
        results = await historical_service.fetch_multiple_symbols(requests, store_data=True)
        
        assert len(results) == 2
        assert all(r.success for r in results)
        assert results[0].symbol == "BTCUSDT"  # Higher priority first
        assert results[1].symbol == "ETHUSDT"
    
    @pytest.mark.asyncio
    async def test_fetch_multiple_symbols_empty(self, historical_service):
        """Test fetching with empty request list."""
        results = await historical_service.fetch_multiple_symbols([])
        assert results == []
    
    @pytest.mark.asyncio
    async def test_detect_data_gaps_no_existing_data(self, historical_service, mock_storage):
        """Test gap detection with no existing data."""
        mock_storage.get_klines.return_value = []
        
        gaps = await historical_service._detect_data_gaps(
            "BTCUSDT", "1m", 1640995200000, 1640998800000
        )
        
        assert len(gaps) == 1
        assert gaps[0] == (1640995200000, 1640998800000)
    
    @pytest.mark.asyncio
    async def test_detect_data_gaps_with_gaps(self, historical_service, mock_storage):
        """Test gap detection with existing data gaps."""
        # Mock existing data with gaps
        kline1 = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=0, L=0, o="45000", c="45010", h="45020", l="45000",
            v="10", n=100, x=True, q="450100", V="5", Q="225050", B="0"
        )
        kline2 = KlineData(
            t=1640995800000, T=1640995859999, s="BTCUSDT", i="1m",  # 10 minutes later
            f=0, L=0, o="45100", c="45110", h="45120", l="45100",
            v="12", n=120, x=True, q="541320", V="6", Q="270660", B="0"
        )
        
        mock_storage.get_klines.return_value = [kline1, kline2]
        
        gaps = await historical_service._detect_data_gaps(
            "BTCUSDT", "1m", 1640995200000, 1640996400000
        )
        
        # Should detect gap between the two K-lines
        assert len(gaps) >= 1
        # Gap should be between first kline end and second kline start
        gap_found = any(gap[0] >= 1640995260000 and gap[1] <= 1640995800000 for gap in gaps)
        assert gap_found
    
    @pytest.mark.asyncio
    async def test_get_available_data_range(self, historical_service, mock_storage):
        """Test getting available data range."""
        # Mock earliest and latest data
        earliest_kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=0, L=0, o="45000", c="45010", h="45020", l="45000",
            v="10", n=100, x=True, q="450100", V="5", Q="225050", B="0"
        )
        latest_kline = KlineData(
            t=1640998800000, T=1640998859999, s="BTCUSDT", i="1m",
            f=0, L=0, o="46000", c="46010", h="46020", l="46000",
            v="15", n=150, x=True, q="690150", V="7", Q="322070", B="0"
        )
        
        mock_storage.get_klines.return_value = [earliest_kline]
        mock_storage.get_latest_kline.return_value = latest_kline
        
        data_range = await historical_service.get_available_data_range("BTCUSDT", "1m")
        
        assert data_range is not None
        assert data_range[0] == 1640995200000
        assert data_range[1] == 1640998800000
    
    @pytest.mark.asyncio
    async def test_get_available_data_range_no_data(self, historical_service, mock_storage):
        """Test getting data range when no data exists."""
        mock_storage.get_klines.return_value = []
        
        data_range = await historical_service.get_available_data_range("BTCUSDT", "1m")
        
        assert data_range is None
    
    def test_deduplicate_klines(self, historical_service):
        """Test K-line deduplication."""
        # Create duplicate K-lines
        kline1 = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=0, L=0, o="45000", c="45010", h="45020", l="45000",
            v="10", n=100, x=True, q="450100", V="5", Q="225050", B="0"
        )
        kline2 = KlineData(  # Duplicate
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=0, L=0, o="45000", c="45010", h="45020", l="45000",
            v="10", n=100, x=True, q="450100", V="5", Q="225050", B="0"
        )
        kline3 = KlineData(  # Different timestamp
            t=1640995260000, T=1640995319999, s="BTCUSDT", i="1m",
            f=0, L=0, o="45010", c="45020", h="45030", l="45010",
            v="12", n=120, x=True, q="540240", V="6", Q="270120", B="0"
        )
        
        duplicates = [kline1, kline2, kline3]
        unique = historical_service._deduplicate_klines(duplicates)
        
        assert len(unique) == 2  # Should remove one duplicate
        assert unique[0].t == 1640995200000
        assert unique[1].t == 1640995260000
    
    def test_get_statistics(self, historical_service):
        """Test getting service statistics."""
        # Set some statistics
        historical_service._total_requests = 10
        historical_service._successful_requests = 8
        historical_service._failed_requests = 2
        historical_service._total_klines_fetched = 1000
        historical_service._rate_limit_hits = 1
        
        stats = historical_service.get_statistics()
        
        assert stats['total_requests'] == 10
        assert stats['successful_requests'] == 8
        assert stats['failed_requests'] == 2
        assert stats['success_rate'] == 80.0
        assert stats['total_klines_fetched'] == 1000
        assert stats['rate_limit_hits'] == 1
        assert stats['max_concurrent_requests'] == 2
        assert stats['chunk_size'] == 100
    
    @pytest.mark.asyncio
    async def test_close(self, historical_service, mock_exchange_client):
        """Test closing the service."""
        await historical_service.close()
        mock_exchange_client.close.assert_called_once()
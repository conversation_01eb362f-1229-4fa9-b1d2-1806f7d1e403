# K-Line API 使用指南

## 快速开始

服务器已经在运行中 (http://localhost:8088)，现在你可以：

### 1. 查看 TradingView 图表界面

打开浏览器访问以下页面：

- **基础版本** (仅 HTTP 轮询): http://localhost:8088/kline.html
- **WebSocket 实时版**: http://localhost:8088/kline-ws.html
- **原始 Demo**: http://localhost:8088/ (使用 TradingView demo 数据)

### 2. API 文档

访问 Swagger UI 查看完整 API 文档：
http://localhost:8088/api/docs

### 3. HTTP API 使用示例

#### 获取历史 K线数据
```bash
# 获取 BTCUSDT 最近的 1分钟 K线数据
curl "http://localhost:8088/api/v1/klines?symbol=BTCUSDT&interval=1m&limit=10"

# 获取指定时间范围的数据
curl "http://localhost:8088/api/v1/klines?symbol=BTCUSDT&interval=15m&start_time=1754149500000&end_time=1754150400000"

# 使用路径参数
curl "http://localhost:8088/api/v1/klines/BTCUSDT/1h?limit=5"
```

#### 获取最新 K线
```bash
curl "http://localhost:8088/api/v1/klines/latest/BTCUSDT/1m"
```

#### 健康检查
```bash
curl "http://localhost:8088/api/v1/health"
```

#### 获取统计信息
```bash
curl "http://localhost:8088/api/v1/stats"
```

### 4. WebSocket 使用示例

WebSocket 端点: `ws://localhost:8088/ws/v1/klines`

#### JavaScript 示例
```javascript
// 连接 WebSocket
const ws = new WebSocket('ws://localhost:8088/ws/v1/klines');

ws.onopen = () => {
    console.log('Connected to WebSocket');
    
    // 订阅 BTCUSDT 1分钟 K线
    ws.send(JSON.stringify({
        action: 'subscribe',
        symbol: 'BTCUSDT',
        interval: '1m'
    }));
};

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
    
    if (data.type === 'kline') {
        // 处理 K线数据
        console.log('New K-line:', data.data);
    }
};
```

#### Python 示例
```python
import asyncio
import websockets
import json

async def subscribe_klines():
    uri = "ws://localhost:8088/ws/v1/klines"
    
    async with websockets.connect(uri) as websocket:
        # 订阅
        await websocket.send(json.dumps({
            "action": "subscribe",
            "symbol": "BTCUSDT",
            "interval": "1m"
        }))
        
        # 接收消息
        async for message in websocket:
            data = json.loads(message)
            print(f"Received: {data}")

asyncio.run(subscribe_klines())
```

### 5. 数据格式

K线数据格式：
```json
{
    "ot": 1754149500000,    // 开盘时间 (毫秒时间戳)
    "ct": 1754150399999,    // 收盘时间 (毫秒时间戳)
    "s": "BTCUSDT",         // 交易对
    "i": "15m",             // 时间间隔
    "ft": 6523455279,       // 第一笔交易ID
    "lt": 6523456082,       // 最后一笔交易ID
    "o": "112884.40",       // 开盘价
    "c": "112841.80",       // 收盘价
    "h": "112888.00",       // 最高价
    "l": "112841.70",       // 最低价
    "v": "37.515",          // 成交量
    "n": 804,               // 交易笔数
    "x": false,             // K线是否已关闭
    "q": "4234389.63450",   // 报价资产成交量
    "bv": "6.098",          // 主动买入基础资产成交量
    "bq": "688293.63750"    // 主动买入报价资产成交量
}
```

### 6. 支持的时间间隔

- `1m` - 1分钟
- `3m` - 3分钟
- `5m` - 5分钟
- `15m` - 15分钟
- `30m` - 30分钟
- `1h` - 1小时
- `4h` - 4小时
- `1d` - 1天

### 7. WebSocket 消息协议

#### 订阅
```json
{
    "action": "subscribe",
    "symbol": "BTCUSDT",
    "interval": "1m"
}
```

#### 取消订阅
```json
{
    "action": "unsubscribe",
    "symbol": "BTCUSDT",
    "interval": "1m"
}
```

#### 心跳
```json
{
    "action": "ping"
}
```

#### 接收的消息类型

**连接成功**
```json
{
    "type": "connected",
    "message": "Connected to K-line WebSocket"
}
```

**订阅成功**
```json
{
    "type": "subscribed",
    "symbol": "BTCUSDT",
    "interval": "1m"
}
```

**K线数据更新**
```json
{
    "type": "kline",
    "data": { /* K线数据 */ }
}
```

**错误消息**
```json
{
    "type": "error",
    "message": "错误描述"
}
```

### 8. 故障排除

1. **无法连接 WebSocket**: 检查防火墙设置，确保 8088 端口可访问
2. **没有实时数据**: 确保 K-line 处理器正在运行并处理数据
3. **TradingView 显示 "No data"**: 检查 Redis 中是否有历史数据

### 9. 性能说明

- HTTP API 支持最多返回 1000 条记录
- WebSocket 实时推送延迟通常在 100ms 以内
- 支持同时订阅多个交易对和时间间隔
- Redis pub/sub 确保多客户端数据同步

### 10. 下一步

1. **集成到你的应用**: 使用提供的 API 接入你的交易系统
2. **自定义 TradingView**: 修改 HTML 文件以适应你的需求
3. **添加认证**: 在生产环境中添加 API 密钥认证
4. **监控和日志**: 使用 `/api/v1/stats` 监控系统状态
"""
WebSocket connection manager with automatic reconnection and error handling.

This module provides comprehensive WebSocket connection management for
real-time data streaming with robust error handling and reconnection logic.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List, Callable, AsyncIterator, Set
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from .reconnection_handler import Reconnect<PERSON><PERSON><PERSON><PERSON>, ReconnectionStrategy
from ..config.settings import settings
from ..models.websocket import WebSocketMessage, StreamType
from ..utils.logger import get_logger


class ConnectionState(Enum):
    """WebSocket connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    CLOSED = "closed"


@dataclass
class ConnectionStats:
    """WebSocket connection statistics."""
    total_connections: int = 0
    successful_connections: int = 0
    failed_connections: int = 0
    reconnections: int = 0
    messages_received: int = 0
    messages_sent: int = 0
    connection_uptime: float = 0.0
    last_message_time: Optional[datetime] = None


class WebSocketError(Exception):
    """Base exception for WebSocket errors."""
    pass


class ConnectionError(WebSocketError):
    """Exception raised for connection errors."""
    pass


class WebSocketManager:
    """
    WebSocket connection manager with automatic reconnection.
    
    This class provides robust WebSocket connection management with
    automatic reconnection, error handling, and message processing.
    """
    
    def __init__(self,
                 base_url: str = None,
                 ping_interval: float = 20.0,
                 ping_timeout: float = 10.0,
                 max_message_size: int = 1024 * 1024,  # 1MB
                 reconnection_strategy: ReconnectionStrategy = ReconnectionStrategy.EXPONENTIAL):
        """
        Initialize WebSocket manager.
        
        Args:
            base_url: Base WebSocket URL
            ping_interval: Ping interval in seconds
            ping_timeout: Ping timeout in seconds
            max_message_size: Maximum message size in bytes
            reconnection_strategy: Reconnection strategy
        """
        self.base_url = base_url or settings.base_url
        self.ping_interval = ping_interval
        self.ping_timeout = ping_timeout
        self.max_message_size = max_message_size
        self.logger = get_logger(self.__class__.__name__)
        
        # Connection management
        self._websocket: Optional[websockets.WebSocketServerProtocol] = None
        self._connection_state = ConnectionState.DISCONNECTED
        self._connection_start_time: Optional[float] = None
        self._connection_url: Optional[str] = None
        
        # Reconnection handling
        self.reconnection_handler = ReconnectionHandler(strategy=reconnection_strategy)
        self._reconnection_task: Optional[asyncio.Task] = None
        self._should_reconnect = True
        
        # Message handling
        self._message_handlers: Dict[StreamType, List[Callable]] = {}
        self._subscribed_streams: Set[str] = set()
        self._message_queue: asyncio.Queue = asyncio.Queue()
        
        # Statistics
        self._stats = ConnectionStats()
        
        # Tasks
        self._ping_task: Optional[asyncio.Task] = None
        self._message_processor_task: Optional[asyncio.Task] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
    
    @property
    def is_connected(self) -> bool:
        """Check if WebSocket is connected."""
        if self._connection_state != ConnectionState.CONNECTED:
            self.logger.debug(f"🔍 is_connected=False: connection_state={self._connection_state.value}")
            return False
            
        if self._websocket is None:
            self.logger.debug(f"🔍 is_connected=False: websocket is None")
            return False
        
        try:
            # Check if websocket is closed - more robust approach
            if hasattr(self._websocket, 'closed'):
                closed = self._websocket.closed
                result = not closed
                self.logger.debug(f"🔍 is_connected={result}: websocket.closed={closed}")
                return result
            else:
                self.logger.debug(f"🔍 is_connected=True: no 'closed' attribute, assuming connected")
                return True
        except Exception as e:
            self.logger.warning(f"❌ Error checking WebSocket connection status: {e}")
            return False
    
    @property
    def connection_state(self) -> ConnectionState:
        """Get current connection state."""
        return self._connection_state
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def connect(self, streams: List[str]) -> bool:
        """
        Connect to WebSocket with specified streams.
        
        Args:
            streams: List of stream names to subscribe to
            
        Returns:
            True if connection successful, False otherwise
        """
        if self.is_connected:
            self.logger.warning("Already connected to WebSocket")
            return True
        
        try:
            self._connection_state = ConnectionState.CONNECTING
            self._stats.total_connections += 1
            
            # Build WebSocket URL with streams
            self._connection_url = settings.get_websocket_url(streams)
            self.logger.info(f"🔗 Connecting to WebSocket: {self._connection_url}")
            self.logger.info(f"🔧 Connection params: ping_interval={self.ping_interval}s, ping_timeout={self.ping_timeout}s, max_size={self.max_message_size}")
            
            # Establish connection
            self._websocket = await websockets.connect(
                self._connection_url,
                ping_interval=self.ping_interval,
                ping_timeout=self.ping_timeout,
                max_size=self.max_message_size,
                compression=None,  # Disable compression for better performance
                open_timeout=10  # 10 seconds connection timeout
            )
            
            self.logger.info(f"✅ WebSocket connection established. Remote address: {getattr(self._websocket, 'remote_address', 'unknown')}")
            self.logger.info(f"📊 Connection details: protocol={getattr(self._websocket, 'subprotocol', 'none')}, extensions={getattr(self._websocket, 'extensions', [])}")
            
            self._connection_state = ConnectionState.CONNECTED
            self._connection_start_time = time.time()
            self._subscribed_streams.update(streams)
            self._stats.successful_connections += 1
            
            # Start background tasks
            await self._start_background_tasks()
            
            # Reset reconnection handler on successful connection
            self.reconnection_handler.reset()
            
            self.logger.info(f"Successfully connected to WebSocket with {len(streams)} streams")
            return True
            
        except Exception as e:
            self._connection_state = ConnectionState.DISCONNECTED
            self._stats.failed_connections += 1
            self.logger.error(f"Failed to connect to WebSocket: {type(e).__name__}: {e}")
            
            # Schedule reconnection if enabled
            if self._should_reconnect:
                await self._schedule_reconnection()
            
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket."""
        self._should_reconnect = False
        
        # Cancel reconnection task
        if self._reconnection_task and not self._reconnection_task.done():
            self._reconnection_task.cancel()
        
        # Stop background tasks
        await self._stop_background_tasks()
        
        # Close WebSocket connection
        if self._websocket and hasattr(self._websocket, 'closed') and not self._websocket.closed:
            try:
                await self._websocket.close()
                self.logger.info("WebSocket disconnected")
            except Exception as e:
                self.logger.error(f"Error closing WebSocket: {e}")
        
        self._connection_state = ConnectionState.DISCONNECTED
        self._websocket = None
        self._subscribed_streams.clear()
    
    async def close(self):
        """Close WebSocket manager and cleanup resources."""
        await self.disconnect()
        self._connection_state = ConnectionState.CLOSED
        self.logger.info("WebSocket manager closed")
    
    def add_message_handler(self, stream_type: StreamType, handler: Callable[[WebSocketMessage], None]):
        """
        Add message handler for specific stream type.
        
        Args:
            stream_type: Type of stream to handle
            handler: Handler function for messages
        """
        if stream_type not in self._message_handlers:
            self._message_handlers[stream_type] = []
        
        self._message_handlers[stream_type].append(handler)
        self.logger.debug(f"Added message handler for {stream_type}")
    
    def remove_message_handler(self, stream_type: StreamType, handler: Callable):
        """
        Remove message handler for specific stream type.
        
        Args:
            stream_type: Type of stream
            handler: Handler function to remove
        """
        if stream_type in self._message_handlers:
            try:
                self._message_handlers[stream_type].remove(handler)
                self.logger.debug(f"Removed message handler for {stream_type}")
            except ValueError:
                self.logger.warning(f"Handler not found for {stream_type}")
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """
        Send message through WebSocket.
        
        Args:
            message: Message to send
            
        Returns:
            True if sent successfully, False otherwise
        """
        if not self.is_connected:
            self.logger.warning("Cannot send message: WebSocket not connected")
            return False
        
        try:
            message_json = json.dumps(message)
            await self._websocket.send(message_json)
            self._stats.messages_sent += 1
            self.logger.debug(f"Sent message: {message_json}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            return False
    
    async def subscribe_to_streams(self, streams: List[str]) -> bool:
        """
        Subscribe to additional streams.
        
        Args:
            streams: List of stream names to subscribe to
            
        Returns:
            True if subscription successful, False otherwise
        """
        if not streams:
            return True
        
        # For Binance WebSocket, we need to reconnect with new streams
        # as it doesn't support dynamic subscription on the stream endpoint
        new_streams = list(self._subscribed_streams.union(set(streams)))
        
        self.logger.info(f"Reconnecting with additional streams: {streams}")
        
        # Disconnect and reconnect with new streams
        await self.disconnect()
        return await self.connect(new_streams)
    
    async def unsubscribe_from_streams(self, streams: List[str]) -> bool:
        """
        Unsubscribe from streams.
        
        Args:
            streams: List of stream names to unsubscribe from
            
        Returns:
            True if unsubscription successful, False otherwise
        """
        if not streams:
            return True
        
        # Remove streams from subscribed set
        remaining_streams = list(self._subscribed_streams - set(streams))
        
        if not remaining_streams:
            # No streams left, disconnect
            await self.disconnect()
            return True
        
        self.logger.info(f"Reconnecting without streams: {streams}")
        
        # Reconnect with remaining streams
        await self.disconnect()
        return await self.connect(remaining_streams)
    
    async def _start_background_tasks(self):
        """Start background tasks for connection management."""
        self.logger.info("🚀 Starting background tasks...")
        
        # Start message processor
        self._message_processor_task = asyncio.create_task(self._message_processor())
        self.logger.info(f"📨 Message processor task started: {self._message_processor_task}")
        
        # Start heartbeat task
        self._heartbeat_task = asyncio.create_task(self._heartbeat_monitor())
        self.logger.info(f"💓 Heartbeat monitor task started: {self._heartbeat_task}")
        
        # Give tasks a moment to start
        await asyncio.sleep(0.1)
        
        self.logger.info("✅ All background tasks started successfully")
    
    async def _stop_background_tasks(self):
        """Stop background tasks."""
        tasks = [
            self._message_processor_task,
            self._heartbeat_task,
            self._ping_task
        ]
        
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self._message_processor_task = None
        self._heartbeat_task = None
        self._ping_task = None
        
        self.logger.debug("Stopped background tasks")
    
    async def _message_processor(self):
        """Process incoming WebSocket messages."""
        self.logger.info("🔄 Message processor task started, waiting for messages...")
        try:
            while self.is_connected:
                try:
                    self.logger.debug(f"📡 Waiting for WebSocket message (connection state: {self._connection_state.value})")
                    
                    # Receive message with timeout
                    message_json = await asyncio.wait_for(
                        self._websocket.recv(),
                        timeout=30.0
                    )
                    
                    self._stats.messages_received += 1
                    self._stats.last_message_time = datetime.now(timezone.utc)
                    
                    # Log message receipt with sample content
                    # self.logger.info(f"📨 Received message #{self._stats.messages_received} (length: {len(message_json)} chars)")
                    if len(message_json) > 0:
                        preview = message_json[:200] + "..." if len(message_json) > 200 else message_json
                        self.logger.debug(f"📄 Message preview: {preview}")
                    
                    # Parse and process message
                    await self._process_message(message_json)
                    
                except asyncio.TimeoutError:
                    self.logger.warning("⏰ Message receive timeout - no messages received in 30 seconds")
                    self.logger.info(f"🔍 Connection check: state={self._connection_state.value}, websocket_closed={getattr(self._websocket, 'closed', 'unknown')}")
                    continue
                    
                except ConnectionClosed:
                    self.logger.info("🔌 WebSocket connection closed")
                    break
                    
                except Exception as e:
                    self.logger.error(f"❌ Error processing message: {e}")
                    continue
        
        except Exception as e:
            self.logger.error(f"Message processor error: {e}")
        
        finally:
            # Connection lost, schedule reconnection
            if self._should_reconnect and self._connection_state == ConnectionState.CONNECTED:
                self._connection_state = ConnectionState.DISCONNECTED
                await self._schedule_reconnection()
    
    async def _process_message(self, message_json: str):
        """
        Process received WebSocket message.
        
        Args:
            message_json: Raw JSON message
        """
        try:
            message_data = json.loads(message_json)
            
            # Create WebSocket message object
            ws_message = WebSocketMessage.from_dict(message_data)
            
            # Determine stream type and call appropriate handlers
            stream_type = ws_message.get_stream_type()
            
            if stream_type in self._message_handlers:
                self.logger.debug(f"🔄 Processing {stream_type} message for stream: {ws_message.stream}")
                for handler in self._message_handlers[stream_type]:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(ws_message)
                        else:
                            handler(ws_message)
                    except Exception as e:
                        self.logger.error(f"Message handler error: {e}")
            else:
                self.logger.warning(f"⚠️ No handler for stream type: {stream_type}, stream: {ws_message.stream}, available handlers: {list(self._message_handlers.keys())}")
            
            self.logger.debug(f"Processed message: {stream_type}")
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON message: {e}")
        except Exception as e:
            self.logger.error(f"Failed to process message: {e}")
    
    async def _heartbeat_monitor(self):
        """Monitor connection health with heartbeat."""
        self.logger.info("💓 Heartbeat monitor started")
        try:
            ping_count = 0
            while self.is_connected:
                try:
                    ping_count += 1
                    # Send ping to check connection
                    if self._websocket and hasattr(self._websocket, 'closed') and not self._websocket.closed:
                        self.logger.debug(f"💓 Sending heartbeat ping #{ping_count}")
                        pong_waiter = await self._websocket.ping()
                        await asyncio.wait_for(pong_waiter, timeout=self.ping_timeout)
                        
                        self.logger.debug(f"✅ Heartbeat ping #{ping_count} successful")
                    else:
                        self.logger.warning(f"⚠️ Cannot send ping: websocket closed or unavailable")
                        break
                    
                    # Wait before next ping
                    await asyncio.sleep(self.ping_interval)
                    
                except asyncio.TimeoutError:
                    self.logger.warning(f"⏰ Heartbeat ping #{ping_count} timeout")
                    break
                    
                except Exception as e:
                    self.logger.error(f"❌ Heartbeat ping #{ping_count} error: {e}")
                    break
        
        except Exception as e:
            self.logger.error(f"💥 Heartbeat monitor error: {e}")
        finally:
            self.logger.info("💓 Heartbeat monitor stopped")
    
    async def _schedule_reconnection(self):
        """Schedule reconnection attempt."""
        if not self._should_reconnect:
            return
        
        self._connection_state = ConnectionState.RECONNECTING
        
        # Cancel existing reconnection task
        if self._reconnection_task and not self._reconnection_task.done():
            self._reconnection_task.cancel()
        
        # Start new reconnection task
        self._reconnection_task = asyncio.create_task(self._reconnection_loop())
    
    async def _reconnection_loop(self):
        """Reconnection loop with exponential backoff."""
        try:
            while self._should_reconnect and not self.is_connected:
                delay = await self.reconnection_handler.get_next_delay()
                
                self.logger.info(f"Attempting reconnection in {delay}s "
                               f"(attempt {self.reconnection_handler.attempt_count})")
                
                await asyncio.sleep(delay)
                
                if self._should_reconnect:
                    streams = list(self._subscribed_streams)
                    success = await self.connect(streams)
                    
                    if success:
                        self._stats.reconnections += 1
                        self.logger.info("Reconnection successful")
                        break
                    else:
                        self.logger.warning("Reconnection attempt failed")
        
        except asyncio.CancelledError:
            self.logger.debug("Reconnection loop cancelled")
        except Exception as e:
            self.logger.error(f"Reconnection loop error: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get connection statistics.
        
        Returns:
            Dictionary with connection statistics
        """
        # Calculate uptime
        uptime = 0.0
        if self._connection_start_time and self.is_connected:
            uptime = time.time() - self._connection_start_time
        
        return {
            'connection_state': self._connection_state.value,
            'is_connected': self.is_connected,
            'subscribed_streams': list(self._subscribed_streams),
            'total_connections': self._stats.total_connections,
            'successful_connections': self._stats.successful_connections,
            'failed_connections': self._stats.failed_connections,
            'reconnections': self._stats.reconnections,
            'messages_received': self._stats.messages_received,
            'messages_sent': self._stats.messages_sent,
            'connection_uptime_seconds': round(uptime, 2),
            'last_message_time': self._stats.last_message_time.isoformat() if self._stats.last_message_time else None,
            'reconnection_attempts': self.reconnection_handler.attempt_count,
            'base_url': self.base_url,
            'ping_interval': self.ping_interval,
            'ping_timeout': self.ping_timeout
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
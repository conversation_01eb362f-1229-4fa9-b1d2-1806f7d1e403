/**
 * Simple WebSocket Manager - Creates new connection for each subscription
 * Solves race condition by using separate connections
 */

class SimpleWebSocketManager {
    constructor(baseUrl, onKlineUpdate) {
        this.baseUrl = baseUrl;
        this.onKlineUpdate = onKlineUpdate;
        
        // Current WebSocket connection
        this.ws = null;
        this.currentSubscription = null;
        
        // Reconnection management
        this.reconnectTimeout = null;
        this.reconnectDelay = 1000;
        this.maxReconnectDelay = 30000;
    }

    /**
     * Subscribe to a symbol - creates new WebSocket connection
     */
    async subscribe(symbol, interval) {
        const key = `${symbol}@${interval}`;
        
        console.log(`[SimpleWSManager] Subscribe request for ${key}`);
        
        // If already subscribed to the same key, do nothing
        if (this.currentSubscription === key && this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log(`[SimpleWSManager] Already subscribed to ${key}`);
            return;
        }
        
        // Close existing connection if any
        this.closeConnection();
        
        // Create new connection
        await this.createConnection(symbol, interval);
    }

    /**
     * Create new WebSocket connection for specific subscription
     */
    createConnection(symbol, interval) {
        return new Promise((resolve, reject) => {
            const key = `${symbol}@${interval}`;
            console.log(`[SimpleWSManager] Creating new connection for ${key}`);
            
            try {
                this.ws = new WebSocket(this.baseUrl);
                this.currentSubscription = key;
                
                this.ws.onopen = () => {
                    console.log(`[SimpleWSManager] Connected, subscribing to ${key}`);
                    
                    // Send subscribe message
                    const message = {
                        action: 'subscribe',
                        symbol: symbol,
                        interval: interval
                    };
                    
                    this.ws.send(JSON.stringify(message));
                    console.log(`[SimpleWSManager] Sent subscribe for ${key}`);
                    
                    resolve();
                };

                this.ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        
                        // Handle K-line data
                        if (message.type === 'kline' && message.data) {
                            const bar = {
                                time: message.data.ot,
                                open: parseFloat(message.data.o),
                                high: parseFloat(message.data.h),
                                low: parseFloat(message.data.l),
                                close: parseFloat(message.data.c),
                                volume: parseFloat(message.data.v)
                            };
                            
                            const dataKey = `${message.data.s}@${message.data.i}`;
                            
                            // Only process if it matches current subscription
                            if (dataKey === this.currentSubscription) {
                                this.onKlineUpdate(dataKey, bar);
                            }
                        }
                    } catch (error) {
                        console.error('[SimpleWSManager] Error parsing message:', error);
                    }
                };

                this.ws.onerror = (error) => {
                    console.error(`[SimpleWSManager] WebSocket error for ${key}:`, error);
                    reject(error);
                };

                this.ws.onclose = () => {
                    console.log(`[SimpleWSManager] Connection closed for ${key}`);
                    
                    // Only reconnect if this is still the current subscription
                    if (this.currentSubscription === key) {
                        this.scheduleReconnect(symbol, interval);
                    }
                };
                
            } catch (error) {
                console.error(`[SimpleWSManager] Failed to create connection for ${key}:`, error);
                reject(error);
            }
        });
    }

    /**
     * Close current WebSocket connection
     */
    closeConnection() {
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        
        if (this.ws) {
            console.log(`[SimpleWSManager] Closing connection for ${this.currentSubscription}`);
            
            // Remove event handlers to prevent reconnection
            this.ws.onclose = null;
            this.ws.onerror = null;
            this.ws.onmessage = null;
            this.ws.onopen = null;
            
            // Close the connection
            if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
                this.ws.close();
            }
            
            this.ws = null;
        }
        
        this.currentSubscription = null;
    }

    /**
     * Schedule reconnection for current subscription
     */
    scheduleReconnect(symbol, interval) {
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }

        console.log(`[SimpleWSManager] Scheduling reconnect in ${this.reconnectDelay}ms`);
        
        this.reconnectTimeout = setTimeout(() => {
            console.log(`[SimpleWSManager] Attempting to reconnect...`);
            this.createConnection(symbol, interval).catch(error => {
                console.error('[SimpleWSManager] Reconnection failed:', error);
            });
            
            // Increase delay for next attempt
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay);
        }, this.reconnectDelay);
    }

    /**
     * Unsubscribe - simply close the connection
     */
    unsubscribe(symbol, interval) {
        const key = `${symbol}@${interval}`;
        
        if (this.currentSubscription === key) {
            console.log(`[SimpleWSManager] Unsubscribing from ${key}`);
            this.closeConnection();
        } else {
            console.log(`[SimpleWSManager] Not subscribed to ${key}, current: ${this.currentSubscription}`);
        }
    }

    /**
     * Close all connections
     */
    close() {
        console.log('[SimpleWSManager] Closing WebSocket manager');
        this.closeConnection();
    }

    /**
     * Get debug information
     */
    getDebugInfo() {
        return {
            connected: this.ws && this.ws.readyState === WebSocket.OPEN,
            readyState: this.ws ? this.ws.readyState : 'No connection',
            currentSubscription: this.currentSubscription,
            reconnectScheduled: !!this.reconnectTimeout
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleWebSocketManager;
}
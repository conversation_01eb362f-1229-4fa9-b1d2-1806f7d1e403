"""
Structured logging configuration using structlog.

This module provides centralized logging configuration with structured output,
context management, and different log levels for development and production environments.
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.typing import FilteringBoundLogger


def setup_logging() -> FilteringBoundLogger:
    """
    Set up structured logging with appropriate configuration for the environment.
    
    Returns:
        FilteringBoundLogger: Configured logger instance
    """
    # Lazy import to avoid circular dependency
    try:
        from ..config.settings import settings
        log_level = settings.log_level
    except ImportError:
        # Fallback to INFO if settings can't be imported
        log_level = "INFO"
    
    # Configure standard library logger
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level),
    )
    
    # Configure structlog processors
    processors = [
        # Add log level
        structlog.processors.add_log_level,
        # Add timestamp
        structlog.processors.TimeStamper(fmt="iso"),
        # Add call site information
        structlog.processors.CallsiteParameterAdder(
            parameters=[
                structlog.processors.CallsiteParameter.FILENAME,
                structlog.processors.CallsiteParameter.FUNC_NAME,
                structlog.processors.CallsiteParameter.LINENO,
            ]
        ),
    ]
    
    # Add JSON formatting for production, human-readable for development
    if log_level in ["DEBUG", "INFO"]:
        # Development formatting - more readable
        processors.append(
            structlog.dev.ConsoleRenderer(colors=True)
        )
    else:
        # Production formatting - JSON for parsing
        processors.append(
            structlog.processors.JSONRenderer()
        )
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level)
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger()


def get_logger(name: str = None) -> FilteringBoundLogger:
    """
    Get a logger instance with optional name.
    
    Args:
        name: Logger name, defaults to the calling module
        
    Returns:
        FilteringBoundLogger: Logger instance
    """
    if name:
        return structlog.get_logger(name)
    return structlog.get_logger()


def add_context(**kwargs: Any) -> Dict[str, Any]:
    """
    Add context to log messages.
    
    Args:
        **kwargs: Context key-value pairs
        
    Returns:
        Dict containing the context
    """
    return kwargs


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = get_logger(self.__class__.__name__)
    
    def log_info(self, message: str, **context: Any) -> None:
        """Log info message with context."""
        self.logger.info(message, **context)
    
    def log_warning(self, message: str, **context: Any) -> None:
        """Log warning message with context."""
        self.logger.warning(message, **context)
    
    def log_error(self, message: str, **context: Any) -> None:
        """Log error message with context."""
        self.logger.error(message, **context)
    
    def log_debug(self, message: str, **context: Any) -> None:
        """Log debug message with context."""
        self.logger.debug(message, **context)


# Initialize logging on module import
logger = setup_logging()
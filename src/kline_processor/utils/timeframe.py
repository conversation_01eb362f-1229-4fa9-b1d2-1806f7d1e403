"""
Timeframe calculation utilities.

This module provides utilities for working with trading timeframes,
including conversions, timestamp alignment, and interval calculations.
"""

import re
from datetime import datetime, timezone
from typing import Dict, Optional, List
from enum import Enum


class TimeframeUnit(Enum):
    """Enumeration of timeframe units."""
    MINUTE = "m"
    HOUR = "h" 
    DAY = "d"
    WEEK = "w"
    MONTH = "M"


class TimeframeUtils:
    """Utility class for timeframe calculations and timestamp alignment."""
    
    # Timeframe patterns and their millisecond values
    _TIMEFRAME_MAP: Dict[str, int] = {
        # Minutes
        "1m": 60 * 1000,
        "3m": 3 * 60 * 1000,
        "5m": 5 * 60 * 1000,
        "15m": 15 * 60 * 1000,
        "30m": 30 * 60 * 1000,
        
        # Hours
        "1h": 60 * 60 * 1000,
        "2h": 2 * 60 * 60 * 1000,
        "4h": 4 * 60 * 60 * 1000,
        "6h": 6 * 60 * 60 * 1000,
        "8h": 8 * 60 * 60 * 1000,
        "12h": 12 * 60 * 60 * 1000,
        
        # Days
        "1d": 24 * 60 * 60 * 1000,
        "3d": 3 * 24 * 60 * 60 * 1000,
        
        # Weeks
        "1w": 7 * 24 * 60 * 60 * 1000,
        
        # Months (approximate - 30 days)
        "1M": 30 * 24 * 60 * 60 * 1000,
    }
    
    @staticmethod
    def get_timeframe_milliseconds(timeframe: str) -> int:
        """
        Convert a timeframe string to milliseconds.
        
        Args:
            timeframe: Timeframe string (e.g., "1m", "5m", "1h", "1d")
            
        Returns:
            Number of milliseconds in the timeframe
            
        Raises:
            ValueError: If timeframe is not supported
            
        Examples:
            >>> TimeframeUtils.get_timeframe_milliseconds("1m")
            60000
            >>> TimeframeUtils.get_timeframe_milliseconds("1h")
            3600000
        """
        if timeframe not in TimeframeUtils._TIMEFRAME_MAP:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        return TimeframeUtils._TIMEFRAME_MAP[timeframe]
    
    @staticmethod
    def align_timestamp_to_timeframe(timestamp: int, timeframe: str) -> int:
        """
        Align a timestamp to the beginning of its timeframe period.
        
        Args:
            timestamp: Timestamp in milliseconds
            timeframe: Timeframe string (e.g., "1m", "5m", "1h", "1d")
            
        Returns:
            Aligned timestamp in milliseconds
            
        Raises:
            ValueError: If timeframe is not supported
            
        Examples:
            >>> # Align to 1-minute boundary
            >>> TimeframeUtils.align_timestamp_to_timeframe(1640995230000, "1m")
            1640995200000
            
            >>> # Align to 1-hour boundary  
            >>> TimeframeUtils.align_timestamp_to_timeframe(1640995230000, "1h")
            1640995200000
        """
        timeframe_ms = TimeframeUtils.get_timeframe_milliseconds(timeframe)
        
        # For month timeframes, use special handling
        # NOTE: _TIMEFRAME_MAP currently defines "1M" as an approximate 30-day duration.
        # To avoid ambiguity with natural months, alignment uses natural month start (UTC),
        # while divisibility checks rely on _TIMEFRAME_MAP durations. If your business
        # requires strict natural-month aggregation, avoid mixing 1M with lower fixed frames.
        if timeframe == "1M":
            return TimeframeUtils._align_to_month(timestamp)
        
        # For week timeframes, align to Monday 00:00 UTC
        if timeframe == "1w":
            return TimeframeUtils._align_to_week(timestamp)
        
        # For day timeframes, align to midnight UTC (natural day)
        if timeframe.endswith('d'):
            return TimeframeUtils._align_to_day(timestamp, int(timeframe[:-1]))
        
        # For minutes and hours, use simple modulo alignment
        return (timestamp // timeframe_ms) * timeframe_ms
    
    @staticmethod
    def _align_to_month(timestamp: int) -> int:
        """Align timestamp to the beginning of the month."""
        dt = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
        aligned_dt = dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        return int(aligned_dt.timestamp() * 1000)
    
    @staticmethod
    def _align_to_week(timestamp: int) -> int:
        """Align timestamp to Monday 00:00 UTC using pure epoch math to avoid calendar overflow."""
        # Convert to seconds
        ts_sec = timestamp // 1000
        # Monday 1970-01-05 00:00:00 UTC is the first Monday after Unix epoch
        # Its timestamp is 345600 (4 days after 1970-01-01).
        MONDAY_1970_01_05_UTC_SEC = 345600
        WEEK_SEC = 7 * 24 * 60 * 60

        # Shift origin to Monday, then floor to whole weeks, then shift back
        aligned_weeks = (ts_sec - MONDAY_1970_01_05_UTC_SEC) // WEEK_SEC
        aligned_sec = aligned_weeks * WEEK_SEC + MONDAY_1970_01_05_UTC_SEC

        return aligned_sec * 1000
    
    @staticmethod
    def _align_to_day(timestamp: int, day_count: int) -> int:
        """Align timestamp to the beginning of a day period."""
        dt = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
        aligned_dt = dt.replace(hour=0, minute=0, second=0, microsecond=0)
        
        if day_count > 1:
            # For multi-day periods, align to epoch-based intervals
            epoch_days = int(aligned_dt.timestamp()) // (24 * 60 * 60)
            aligned_days = (epoch_days // day_count) * day_count
            aligned_timestamp = aligned_days * 24 * 60 * 60
            return aligned_timestamp * 1000
        
        return int(aligned_dt.timestamp() * 1000)
    
    @staticmethod
    def get_next_timeframe_timestamp(timestamp: int, timeframe: str) -> int:
        """
        Get the timestamp for the next timeframe period.
        
        Args:
            timestamp: Current timestamp in milliseconds
            timeframe: Timeframe string
            
        Returns:
            Next period timestamp in milliseconds
            
        Examples:
            >>> TimeframeUtils.get_next_timeframe_timestamp(1640995200000, "1m")
            1640995260000
        """
        aligned = TimeframeUtils.align_timestamp_to_timeframe(timestamp, timeframe)
        timeframe_ms = TimeframeUtils.get_timeframe_milliseconds(timeframe)
        return aligned + timeframe_ms
    
    @staticmethod
    def get_previous_timeframe_timestamp(timestamp: int, timeframe: str) -> int:
        """
        Get the timestamp for the previous timeframe period.
        
        Args:
            timestamp: Current timestamp in milliseconds
            timeframe: Timeframe string
            
        Returns:
            Previous period timestamp in milliseconds
            
        Examples:
            >>> TimeframeUtils.get_previous_timeframe_timestamp(1640995260000, "1m")
            1640995200000
        """
        aligned = TimeframeUtils.align_timestamp_to_timeframe(timestamp, timeframe)
        timeframe_ms = TimeframeUtils.get_timeframe_milliseconds(timeframe)
        return aligned - timeframe_ms
    
    @staticmethod
    def is_valid_timeframe(timeframe: str) -> bool:
        """
        Check if a timeframe string is valid.
        
        Args:
            timeframe: Timeframe string to validate
            
        Returns:
            True if timeframe is valid, False otherwise
            
        Examples:
            >>> TimeframeUtils.is_valid_timeframe("1m")
            True
            >>> TimeframeUtils.is_valid_timeframe("2m")
            False
        """
        return timeframe in TimeframeUtils._TIMEFRAME_MAP
    
    @staticmethod
    def get_supported_timeframes() -> List[str]:
        """
        Get list of all supported timeframes.
        
        Returns:
            List of supported timeframe strings
        """
        return list(TimeframeUtils._TIMEFRAME_MAP.keys())
    
    @staticmethod
    def parse_timeframe(timeframe: str) -> tuple[int, TimeframeUnit]:
        """
        Parse a timeframe string into number and unit.
        
        Args:
            timeframe: Timeframe string (e.g., "5m", "1h", "1d")
            
        Returns:
            Tuple of (number, TimeframeUnit)
            
        Raises:
            ValueError: If timeframe format is invalid
            
        Examples:
            >>> TimeframeUtils.parse_timeframe("5m")
            (5, TimeframeUnit.MINUTE)
            >>> TimeframeUtils.parse_timeframe("1h")
            (1, TimeframeUnit.HOUR)
        """
        pattern = r'^(\d+)([mhdwM])$'
        match = re.match(pattern, timeframe)
        
        if not match:
            raise ValueError(f"Invalid timeframe format: {timeframe}")
        
        number = int(match.group(1))
        unit_str = match.group(2)
        
        try:
            unit = TimeframeUnit(unit_str)
        except ValueError:
            raise ValueError(f"Invalid timeframe unit: {unit_str}")
        
        return number, unit
    
    @staticmethod
    def format_timeframe(number: int, unit: TimeframeUnit) -> str:
        """
        Format a timeframe from number and unit.
        
        Args:
            number: Number of units
            unit: TimeframeUnit enum value
            
        Returns:
            Formatted timeframe string
            
        Examples:
            >>> TimeframeUtils.format_timeframe(5, TimeframeUnit.MINUTE)
            "5m"
            >>> TimeframeUtils.format_timeframe(1, TimeframeUnit.HOUR)
            "1h"
        """
        return f"{number}{unit.value}"
    
    @staticmethod
    def get_timeframe_hierarchy() -> Dict[str, List[str]]:
        """
        Get timeframe hierarchy for aggregation.
        
        Returns:
            Dictionary mapping base timeframes to higher timeframes
            
        Examples:
            >>> hierarchy = TimeframeUtils.get_timeframe_hierarchy()
            >>> hierarchy["1m"]
            ["3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M"]
        """
        timeframes = TimeframeUtils.get_supported_timeframes()
        hierarchy = {}
        
        for base_tf in timeframes:
            base_ms = TimeframeUtils.get_timeframe_milliseconds(base_tf)
            higher_tfs = []
            
            for tf in timeframes:
                tf_ms = TimeframeUtils.get_timeframe_milliseconds(tf)
                # Check if this timeframe is a higher multiple of the base
                if tf_ms > base_ms and tf_ms % base_ms == 0:
                    higher_tfs.append(tf)
            
            # Sort by duration
            higher_tfs.sort(key=lambda x: TimeframeUtils.get_timeframe_milliseconds(x))
            hierarchy[base_tf] = higher_tfs
        
        return hierarchy
    
    @staticmethod
    def can_aggregate_to(from_timeframe: str, to_timeframe: str) -> bool:
        """
        Check if one timeframe can be aggregated to another.
        
        Args:
            from_timeframe: Source timeframe
            to_timeframe: Target timeframe
            
        Returns:
            True if aggregation is possible, False otherwise
            
        Examples:
            >>> TimeframeUtils.can_aggregate_to("1m", "5m")
            True
            >>> TimeframeUtils.can_aggregate_to("5m", "1m")
            False
        """
        if not TimeframeUtils.is_valid_timeframe(from_timeframe):
            return False
        if not TimeframeUtils.is_valid_timeframe(to_timeframe):
            return False
        
        from_ms = TimeframeUtils.get_timeframe_milliseconds(from_timeframe)
        to_ms = TimeframeUtils.get_timeframe_milliseconds(to_timeframe)
        
        # Can aggregate if target timeframe is larger and evenly divisible
        return to_ms > from_ms and to_ms % from_ms == 0
    
    @staticmethod
    def timestamp_to_human_readable(timestamp: int) -> str:
        """
        Convert timestamp to human-readable format.
        
        Args:
            timestamp: Timestamp in milliseconds
            
        Returns:
            Human-readable datetime string in UTC
            
        Examples:
            >>> TimeframeUtils.timestamp_to_human_readable(1640995200000)
            "2022-01-01 00:00:00 UTC"
        """
        dt = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
        return dt.strftime("%Y-%m-%d %H:%M:%S UTC")
    
    @staticmethod
    def human_readable_to_timestamp(datetime_str: str) -> int:
        """
        Convert human-readable datetime to timestamp.
        
        Args:
            datetime_str: Datetime string in format "YYYY-MM-DD HH:MM:SS"
            
        Returns:
            Timestamp in milliseconds
            
        Examples:
            >>> TimeframeUtils.human_readable_to_timestamp("2022-01-01 00:00:00")
            1640995200000
        """
        try:
            dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            dt = dt.replace(tzinfo=timezone.utc)
            return int(dt.timestamp() * 1000)
        except ValueError as e:
            raise ValueError(f"Invalid datetime format: {datetime_str}. Expected: YYYY-MM-DD HH:MM:SS") from e
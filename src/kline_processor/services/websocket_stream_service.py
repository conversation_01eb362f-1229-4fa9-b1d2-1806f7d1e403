"""
WebSocket stream service for real-time K-line data processing.

This service provides comprehensive real-time K-line data processing by integrating
WebSocket connections, data aggregation, and storage with pub/sub notifications.
"""

import asyncio
from typing import List, Dict, Optional, Set, Callable, Any
from datetime import datetime, timezone
from dataclasses import dataclass
from collections import defaultdict

from ..websocket.websocket_manager import WebSocketManager, ConnectionState
from ..storage.kline_storage import KlineStorage
from ..models.websocket import WebSocketMessage, StreamType
from ..models.kline import KlineData
from ..utils.timeframe import TimeframeUtils
from ..utils.logger import get_logger


@dataclass
class StreamSubscription:
    """WebSocket stream subscription configuration."""
    symbol: str
    interval: str
    auto_aggregate: bool = True
    target_intervals: List[str] = None
    
    def __post_init__(self):
        if self.target_intervals is None:
            self.target_intervals = []


@dataclass
class StreamStatistics:
    """Real-time stream processing statistics."""
    total_messages: int = 0
    kline_messages: int = 0
    processed_klines: int = 0
    aggregated_klines: int = 0
    storage_operations: int = 0
    failed_operations: int = 0
    last_message_time: Optional[datetime] = None
    uptime_seconds: float = 0.0


class WebSocketStreamService:
    """
    Service for real-time WebSocket K-line data processing.
    
    This service manages WebSocket connections, processes incoming K-line data,
    performs real-time aggregation, and stores data with pub/sub notifications.
    """
    
    def __init__(self,
                 websocket_manager: Optional[WebSocketManager] = None,
                 storage: Optional[KlineStorage] = None,
                 auto_reconnect: bool = True,
                 buffer_size: int = 1000,
                 flush_interval_ms: Optional[int] = None):
        """
        Initialize WebSocket stream service.
        
        Args:
            websocket_manager: WebSocket connection manager
            storage: K-line storage instance
            auto_reconnect: Whether to auto-reconnect on disconnection
            buffer_size: Message buffer size for batch processing
            flush_interval_ms: Interval to flush message buffer (ms)
        """
        self.websocket_manager = websocket_manager or WebSocketManager()
        self.storage = storage or KlineStorage()
        self.auto_reconnect = auto_reconnect
        self.buffer_size = buffer_size
        self.logger = get_logger(self.__class__.__name__)
        # Configurable flush interval (ms). Prefer explicit param; fallback to Settings.
        try:
            from ..config.settings import Settings
            self.flush_interval_ms = int(flush_interval_ms if flush_interval_ms is not None else Settings().stream_flush_interval_ms)
        except Exception:
            self.flush_interval_ms = int(flush_interval_ms if flush_interval_ms is not None else 1000)
        
        # Service state
        self._is_running = False
        self._start_time: Optional[float] = None
        
        # Subscriptions and handlers
        self._subscriptions: Dict[str, StreamSubscription] = {}
        self._message_handlers: List[Callable[[WebSocketMessage], None]] = []
        self._kline_handlers: List[Callable[[KlineData], None]] = []
        
        # Message buffering for batch processing
        self._message_buffer: List[KlineData] = []
        self._buffer_lock = asyncio.Lock()
        self._buffer_flush_task: Optional[asyncio.Task] = None
        
        # Statistics
        self._stats = StreamStatistics()
        
        # Background tasks
        self._buffer_processor_task: Optional[asyncio.Task] = None
        self._statistics_task: Optional[asyncio.Task] = None

        # Processing gates (symbol@interval -> enabled)
        self._processing_enabled: Dict[str, bool] = {}

        # Redis client for runtime flags (lazy import to avoid cycles)
        try:
            from ..storage.redis_client import RedisClient
            self._redis_client = RedisClient()
        except Exception:
            self._redis_client = None

        # Processing gates (symbol@interval -> enabled)
        self._processing_enabled: Dict[str, bool] = {}

        # Redis client for runtime flags (lazy import to avoid cycles)
        try:
            from ..storage.redis_client import RedisClient
            self._redis_client = RedisClient()
        except Exception:
            self._redis_client = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()
    
    async def start(self):
        """Start the WebSocket stream service."""
        if self._is_running:
            self.logger.warning("WebSocket stream service is already running")
            return
        
        try:
            self._is_running = True
            self._start_time = asyncio.get_event_loop().time()
            
            # Register message handlers
            self.websocket_manager.add_message_handler(
                StreamType.KLINE, self._handle_kline_message
            )
            
            # Start background tasks
            self._buffer_processor_task = asyncio.create_task(self._buffer_processor())
            self._statistics_task = asyncio.create_task(self._statistics_updater())
            self.logger.info(f"WebSocket stream service started successfully with flush interval {self.flush_interval_ms} ms")
            
        except Exception as e:
            self._is_running = False
            self.logger.error(f"Failed to start WebSocket stream service: {e}")
            raise
    
    async def stop(self):
        """Stop the WebSocket stream service."""
        if not self._is_running:
            return
        
        # Mark as not running immediately
        self._is_running = False
        
        try:
            # Stop background tasks first
            await self._stop_background_tasks()
            
            # Flush remaining buffer with timeout
            try:
                await asyncio.wait_for(self._flush_buffer(), timeout=2.0)
            except asyncio.TimeoutError:
                self.logger.warning("Buffer flush timeout during shutdown")
            
            # Disconnect WebSocket with timeout
            try:
                await asyncio.wait_for(self.websocket_manager.disconnect(), timeout=3.0)
            except asyncio.TimeoutError:
                self.logger.warning("WebSocket disconnect timeout")
            
            self.logger.info("WebSocket stream service stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping WebSocket stream service: {e}")
    
    async def subscribe_to_streams(self, subscriptions: List[StreamSubscription]) -> bool:
        """
        Subscribe to WebSocket streams.
        
        Args:
            subscriptions: List of stream subscriptions
            
        Returns:
            True if subscription successful, False otherwise
        """
        if not subscriptions:
            return True
        
        try:
            # Build stream names from subscriptions
            stream_names = []
            for sub in subscriptions:
                stream_name = f"{sub.symbol.lower()}@kline_{sub.interval}"
                stream_names.append(stream_name)
                
                # Store subscription
                self._subscriptions[stream_name] = sub

                # Initialize processing gate as disabled by default; will be enabled after HTTP sync
                self._processing_enabled[f"{sub.symbol.upper()}:{sub.interval}"] = False
                
                self.logger.info(f"Added subscription: {stream_name} (processing enabled: False)")
            
            # Subscribe to WebSocket streams
            success = await self.websocket_manager.subscribe_to_streams(stream_names)
            
            if success:
                self.logger.info(f"Successfully subscribed to {len(stream_names)} streams")
                return True
            else:
                self.logger.error("Failed to subscribe to WebSocket streams")
                return False
                
        except Exception as e:
            self.logger.error(f"Error subscribing to streams: {e}")
            return False
    
    async def unsubscribe_from_streams(self, stream_names: List[str]) -> bool:
        """
        Unsubscribe from WebSocket streams.
        
        Args:
            stream_names: List of stream names to unsubscribe from
            
        Returns:
            True if unsubscription successful, False otherwise
        """
        if not stream_names:
            return True
        
        try:
            # Remove subscriptions
            for stream_name in stream_names:
                if stream_name in self._subscriptions:
                    del self._subscriptions[stream_name]
                    self.logger.info(f"Removed subscription: {stream_name}")
            
            # Unsubscribe from WebSocket
            success = await self.websocket_manager.unsubscribe_from_streams(stream_names)
            
            if success:
                self.logger.info(f"Successfully unsubscribed from {len(stream_names)} streams")
                return True
            else:
                self.logger.error("Failed to unsubscribe from WebSocket streams")
                return False
                
        except Exception as e:
            self.logger.error(f"Error unsubscribing from streams: {e}")
            return False
    
    def add_message_handler(self, handler: Callable[[WebSocketMessage], None]):
        """
        Add custom message handler.
        
        Args:
            handler: Message handler function
        """
        self._message_handlers.append(handler)
        self.logger.debug("Added custom message handler")
    
    def add_kline_handler(self, handler: Callable[[KlineData], None]):
        """
        Add custom K-line handler.
        
        Args:
            handler: K-line handler function
        """
        self._kline_handlers.append(handler)
        self.logger.debug("Added custom K-line handler")

    async def set_processing_enabled(self, symbol: str, interval: str, enabled: bool) -> None:
        """
        Enable/disable WS processing for a specific symbol@interval and mirror to Redis.
        """
        key = f"{symbol.upper()}:{interval}"
        self._processing_enabled[key] = bool(enabled)
        self.logger.info(f"WS processing gate set to {enabled} for {key}")
        if getattr(self, "_redis_client", None):
            try:
                await self._redis_client.set_flag(f"kline:ws:enabled:{symbol.upper()}:{interval}", 1 if enabled else 0)
            except Exception as e:
                self.logger.warning(f"Failed to set ws-enabled flag in Redis for {key}: {e}")

    async def set_processing_enabled(self, symbol: str, interval: str, enabled: bool) -> None:
        """
        Enable/disable processing for a specific symbol@interval.
        Also sync a Redis runtime flag so other components can check.
        """
        key = f"{symbol.upper()}:{interval}"
        self._processing_enabled[key] = bool(enabled)
        self.logger.info(f"WS processing gate set to {enabled} for {key}")
        # Mirror to Redis if available
        if self._redis_client:
            try:
                # Persist runtime ws-enabled flag
                await self._redis_client.set_flag(f"kline:ws:enabled:{symbol.upper()}:{interval}", 1 if enabled else 0)
            except Exception as e:
                self.logger.warning(f"Failed to set ws-enabled flag in Redis for {key}: {e}")
    
    async def _handle_kline_message(self, message: WebSocketMessage):
        """
        Handle incoming K-line WebSocket message.
        
        Args:
            message: WebSocket message containing K-line data
        """
        try:
            self._stats.total_messages += 1
            self._stats.last_message_time = datetime.now(timezone.utc)
            
            # Log K-line message processing
            if self._stats.total_messages % 10 == 1:  # Log every 10th message
                self.logger.info(f"🔷 Processing K-line message #{self._stats.total_messages} from stream: {message.stream}")
            
            # Call custom message handlers
            for handler in self._message_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(message)
                    else:
                        handler(message)
                except Exception as e:
                    self.logger.error(f"Message handler error: {e}")
            
            # Convert to K-line data
            kline_data = message.to_kline_data()
            if not kline_data:
                self.logger.warning(f"⚠️ Failed to convert message to K-line data from stream: {message.stream}")
                # Debug: Show message structure
                if hasattr(message, 'data') and self._stats.total_messages % 10 == 1:
                    import json
                    self.logger.warning(f"🔍 Sample message data keys: {list(message.data.keys()) if isinstance(message.data, dict) else 'not dict'}")
                    if isinstance(message.data, dict) and len(str(message.data)) < 500:
                        self.logger.warning(f"🔍 Sample message content: {json.dumps(message.data, indent=2)}")
                return
            
            self._stats.kline_messages += 1
            self.logger.debug(f"✅ Converted message to K-line data: {kline_data.s}@{kline_data.i}, price: {kline_data.c}")
            
            # Debug: Log sample K-line data to check format
            if self._stats.kline_messages % 20 == 1:
                self.logger.info(f"🔍 Sample K-line data: symbol={kline_data.s}, interval={kline_data.i}, open_time={kline_data.t}, close_time={kline_data.T}, price={kline_data.c}")
            
            # Call custom K-line handlers
            for handler in self._kline_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(kline_data)
                    else:
                        handler(kline_data)
                except Exception as e:
                    self.logger.error(f"K-line handler error: {e}")
            
            # Add to buffer for batch processing
            async with self._buffer_lock:
                self._message_buffer.append(kline_data)
                
                # Flush buffer if it's full
                if len(self._message_buffer) >= self.buffer_size:
                    await self._flush_buffer()
            
        except Exception as e:
            self._stats.failed_operations += 1
            self.logger.error(f"Error handling K-line message: {e}")
    
    async def _buffer_processor(self):
        """Background task for processing message buffer."""
        try:
            while self._is_running:
                try:
                    # Wait for buffer to have data or timeout (configurable)
                    await asyncio.sleep(max(0.01, (getattr(self, "flush_interval_ms", 1000)) / 1000.0))
                    
                    if self._message_buffer:
                        await self._flush_buffer()
                        
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Buffer processor error: {e}")
                    
        except Exception as e:
            self.logger.error(f"Buffer processor task error: {e}")
    
    async def _flush_buffer(self):
        """Flush the message buffer and process K-lines."""
        if not self._message_buffer:
            return
        
        async with self._buffer_lock:
            buffer_copy = self._message_buffer.copy()
            self._message_buffer.clear()
        
        if not buffer_copy:
            return
        
        try:
            # Group K-lines by symbol and interval
            grouped_klines = self._group_klines_by_stream(buffer_copy)
            
            # Process each group
            for stream_key, klines in grouped_klines.items():
                await self._process_kline_group(stream_key, klines)
            
            self._stats.processed_klines += len(buffer_copy)
            
        except Exception as e:
            self._stats.failed_operations += 1
            self.logger.error(f"Error flushing buffer: {e}")
    
    def _group_klines_by_stream(self, klines: List[KlineData]) -> Dict[str, List[KlineData]]:
        """
        Group K-lines by stream (symbol + interval).
        
        Args:
            klines: List of K-line data
            
        Returns:
            Dictionary mapping stream keys to K-lines
        """
        grouped = defaultdict(list)
        
        for kline in klines:
            stream_key = f"{kline.s.lower()}@kline_{kline.i}"
            grouped[stream_key].append(kline)
        
        return dict(grouped)
    
    async def _process_kline_group(self, stream_key: str, klines: List[KlineData]):
        """
        Process a group of K-lines for a specific stream with remove-then-insert pattern.
        
        This method now implements the remove-then-insert pattern for each K-line,
        ensuring atomic replacement of K-line data at specific timestamps.
        
        Args:
            stream_key: Stream identifier (e.g., "btcusdt@kline_1m")
            klines: List of K-lines to process
        """
        try:
            # Check processing gate first
            if not klines:
                return
            
            # Extract symbol and interval from the first kline for gate check
            first_kline = klines[0]
            symbol = first_kline.s
            interval = first_kline.i
            gate_key = f"{symbol.upper()}:{interval}"
            
            # Check if processing is enabled for this symbol@interval
            if not self._processing_enabled.get(gate_key, False):
                self.logger.debug(f"🚫 Processing disabled for {gate_key}, skipping {len(klines)} klines")
                return
            
            self.logger.debug(f"✅ Processing enabled for {gate_key}, processing {len(klines)} klines")
            
            # Process each K-line with remove-then-insert pattern
            processed_count = 0
            for kline in klines:
                # 1. Remove existing K-line at this timestamp
                removed = await self.storage.remove_kline_by_timestamp(
                    symbol, interval, kline.t
                )
                
                # 2. Insert new K-line
                stored = await self.storage.store_kline(kline, publish=True)
                
                if stored:
                    processed_count += 1
                else:
                    self.logger.warning(f"Failed to store K-line for {symbol}:{interval} at {kline.t}")
            
            self._stats.storage_operations += 1
            self._stats.processed_klines += processed_count
            
            self.logger.debug(f"✅ Processed {processed_count}/{len(klines)} K-lines for {stream_key} using remove-then-insert pattern")
            
            # Note: Aggregation has been removed as we now subscribe directly to all intervals
            
        except Exception as e:
            self._stats.failed_operations += 1
            self.logger.error(f"Error processing K-line group {stream_key}: {e}")
    
    
    async def _statistics_updater(self):
        """Background task for updating statistics."""
        try:
            while self._is_running:
                try:
                    # Update uptime
                    if self._start_time:
                        current_time = asyncio.get_event_loop().time()
                        self._stats.uptime_seconds = current_time - self._start_time
                    
                    # Wait before next update
                    await asyncio.sleep(10.0)  # Update every 10 seconds
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Statistics updater error: {e}")
                    
        except Exception as e:
            self.logger.error(f"Statistics updater task error: {e}")
    
    async def _stop_background_tasks(self):
        """Stop all background tasks."""
        tasks = [
            self._buffer_processor_task,
            self._statistics_task
        ]
        
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self._buffer_processor_task = None
        self._statistics_task = None
        
        self.logger.debug("Stopped background tasks")
    
    @property
    def is_running(self) -> bool:
        """Check if the service is running."""
        return self._is_running
    
    @property
    def is_connected(self) -> bool:
        """Check if WebSocket is connected."""
        return self.websocket_manager.is_connected
    
    @property
    def connection_state(self) -> ConnectionState:
        """Get current WebSocket connection state."""
        return self.websocket_manager.connection_state
    
    def get_subscriptions(self) -> Dict[str, StreamSubscription]:
        """
        Get current stream subscriptions.
        
        Returns:
            Dictionary of active subscriptions
        """
        return self._subscriptions.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get service statistics.
        
        Returns:
            Dictionary with service statistics
        """
        return {
            'is_running': self._is_running,
            'is_connected': self.is_connected,
            'connection_state': self.connection_state.value,
            'total_messages': self._stats.total_messages,
            'kline_messages': self._stats.kline_messages,
            'processed_klines': self._stats.processed_klines,
            'aggregated_klines': self._stats.aggregated_klines,
            'storage_operations': self._stats.storage_operations,
            'failed_operations': self._stats.failed_operations,
            'uptime_seconds': round(self._stats.uptime_seconds, 2),
            'last_message_time': self._stats.last_message_time.isoformat() if self._stats.last_message_time else None,
            'active_subscriptions': len(self._subscriptions),
            'buffer_size': len(self._message_buffer),
            'buffer_capacity': self.buffer_size,
            'websocket_stats': self.websocket_manager.get_statistics()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the service.
        
        Returns:
            Health check results
        """
        health = {
            'healthy': True,
            'issues': [],
            'service_running': self._is_running,
            'websocket_connected': self.is_connected,
            'active_subscriptions': len(self._subscriptions),
            'buffer_utilization': len(self._message_buffer) / self.buffer_size if self.buffer_size > 0 else 0,
            'recent_activity': False
        }
        
        # Check for recent activity
        if self._stats.last_message_time:
            time_since_last = (datetime.now(timezone.utc) - self._stats.last_message_time).total_seconds()
            health['recent_activity'] = time_since_last < 60  # Activity within last minute
        
        # Check for issues
        if not self._is_running:
            health['healthy'] = False
            health['issues'].append('Service not running')
        
        if not self.is_connected and self._subscriptions:
            health['healthy'] = False
            health['issues'].append('WebSocket not connected but has subscriptions')
        
        if self._stats.failed_operations > 0:
            failure_rate = self._stats.failed_operations / max(self._stats.total_messages, 1)
            if failure_rate > 0.1:  # More than 10% failure rate
                health['healthy'] = False
                health['issues'].append(f'High failure rate: {failure_rate:.2%}')
        
        return health


# Global service instance
websocket_stream_service = WebSocketStreamService()
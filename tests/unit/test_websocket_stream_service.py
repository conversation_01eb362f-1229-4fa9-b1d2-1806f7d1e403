"""
Unit tests for WebSocket Stream Service.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from kline_processor.services.websocket_stream_service import (
    WebSocketStreamService, StreamSubscription, StreamStatistics
)
from kline_processor.websocket.websocket_manager import WebSocketManager, ConnectionState
from kline_processor.services.kline_aggregator_service import KlineAggregatorService
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.models.websocket import WebSocketMessage, StreamType
from kline_processor.models.kline import KlineData


@pytest.fixture
def mock_websocket_manager():
    """Create mock WebSocketManager."""
    mock = AsyncMock(spec=WebSocketManager)
    mock.is_connected = True
    mock.connection_state = ConnectionState.CONNECTED
    mock.get_statistics.return_value = {"messages_received": 100}
    return mock


@pytest.fixture
def mock_aggregator_service():
    """Create mock KlineAggregatorService."""
    mock = AsyncMock(spec=KlineAggregatorService)
    mock.validate_aggregation_request.return_value = True
    mock.get_statistics.return_value = {"total_aggregations": 10}
    return mock


@pytest.fixture
def mock_storage():
    """Create mock KlineStorage."""
    mock = AsyncMock(spec=KlineStorage)
    mock.store_klines_batch.return_value = 1
    return mock


@pytest.fixture
def stream_service(mock_websocket_manager, mock_aggregator_service, mock_storage):
    """Create WebSocketStreamService with mocks."""
    return WebSocketStreamService(
        websocket_manager=mock_websocket_manager,
        aggregator_service=mock_aggregator_service,
        storage=mock_storage,
        buffer_size=5
    )


@pytest.fixture
def sample_subscription():
    """Create sample stream subscription."""
    return StreamSubscription(
        symbol="BTCUSDT",
        interval="1m",
        auto_aggregate=True,
        target_intervals=["5m", "1h"]
    )


@pytest.fixture
def sample_kline():
    """Create sample K-line data."""
    return KlineData(
        t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
        f=100, L=199, o="45000.00", c="45010.00", h="45020.00", l="44990.00",
        v="10.5", n=100, x=True, q="472605.0", V="5.2", Q="234050.0", B="0"
    )


@pytest.fixture
def sample_websocket_message():
    """Create sample WebSocket message."""
    message_data = {
        "e": "kline",
        "E": 1640995200000,
        "s": "BTCUSDT",
        "k": {
            "t": 1640995200000,
            "T": 1640995259999,
            "s": "BTCUSDT",
            "i": "1m",
            "f": 100,
            "L": 199,
            "o": "45000.00",
            "c": "45010.00",
            "h": "45020.00",
            "l": "44990.00",
            "v": "10.5",
            "n": 100,
            "x": True,
            "q": "472605.0",
            "V": "5.2",
            "Q": "234050.0",
            "B": "0"
        }
    }
    return WebSocketMessage.from_dict(message_data)


class TestStreamSubscription:
    """Test cases for StreamSubscription."""
    
    def test_stream_subscription_creation(self):
        """Test creating stream subscription."""
        sub = StreamSubscription(
            symbol="BTCUSDT",
            interval="1m",
            auto_aggregate=True,
            target_intervals=["5m", "1h"]
        )
        
        assert sub.symbol == "BTCUSDT"
        assert sub.interval == "1m"
        assert sub.auto_aggregate is True
        assert sub.target_intervals == ["5m", "1h"]
    
    def test_stream_subscription_default_target_intervals(self):
        """Test stream subscription with default target intervals."""
        sub = StreamSubscription(
            symbol="BTCUSDT",
            interval="1m"
        )
        
        assert sub.target_intervals == []
        assert sub.auto_aggregate is True


class TestStreamStatistics:
    """Test cases for StreamStatistics."""
    
    def test_stream_statistics_creation(self):
        """Test creating stream statistics."""
        stats = StreamStatistics()
        
        assert stats.total_messages == 0
        assert stats.kline_messages == 0
        assert stats.processed_klines == 0
        assert stats.aggregated_klines == 0
        assert stats.storage_operations == 0
        assert stats.failed_operations == 0
        assert stats.last_message_time is None
        assert stats.uptime_seconds == 0.0


class TestWebSocketStreamService:
    """Test cases for WebSocketStreamService."""
    
    def test_initialization(self, stream_service):
        """Test service initialization."""
        assert stream_service.buffer_size == 5
        assert stream_service.auto_reconnect is True
        assert not stream_service.is_running
        assert len(stream_service._subscriptions) == 0
        assert len(stream_service._message_handlers) == 0
        assert len(stream_service._kline_handlers) == 0
    
    @pytest.mark.asyncio
    async def test_context_manager(self, stream_service):
        """Test using service as async context manager."""
        with patch.object(stream_service, 'start') as mock_start:
            with patch.object(stream_service, 'stop') as mock_stop:
                async with stream_service as service:
                    assert service is stream_service
                
                mock_start.assert_called_once()
                mock_stop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_success(self, stream_service, mock_websocket_manager):
        """Test successful service start."""
        await stream_service.start()
        
        assert stream_service.is_running
        assert stream_service._start_time is not None
        assert stream_service._buffer_processor_task is not None
        assert stream_service._statistics_task is not None
        
        # Clean up
        await stream_service.stop()
    
    @pytest.mark.asyncio
    async def test_start_already_running(self, stream_service):
        """Test starting service when already running."""
        stream_service._is_running = True
        
        await stream_service.start()
        
        # Should remain running without error
        assert stream_service.is_running
    
    @pytest.mark.asyncio
    async def test_stop_success(self, stream_service):
        """Test successful service stop."""
        # Start first
        await stream_service.start()
        assert stream_service.is_running
        
        # Then stop
        await stream_service.stop()
        
        assert not stream_service.is_running
        assert stream_service._buffer_processor_task is None
        assert stream_service._statistics_task is None
    
    @pytest.mark.asyncio
    async def test_stop_not_running(self, stream_service):
        """Test stopping service when not running."""
        assert not stream_service.is_running
        
        # Should not raise exception
        await stream_service.stop()
        
        assert not stream_service.is_running
    
    @pytest.mark.asyncio
    async def test_subscribe_to_streams_success(self, stream_service, mock_websocket_manager, sample_subscription):
        """Test successful stream subscription."""
        mock_websocket_manager.subscribe_to_streams.return_value = True
        
        result = await stream_service.subscribe_to_streams([sample_subscription])
        
        assert result is True
        assert len(stream_service._subscriptions) == 1
        expected_stream = "btcusdt@kline_1m"
        assert expected_stream in stream_service._subscriptions
        
        # Verify WebSocket was called with correct stream name
        mock_websocket_manager.subscribe_to_streams.assert_called_once_with([expected_stream])
    
    @pytest.mark.asyncio
    async def test_subscribe_to_streams_empty(self, stream_service):
        """Test subscribing to empty stream list."""
        result = await stream_service.subscribe_to_streams([])
        assert result is True
    
    @pytest.mark.asyncio
    async def test_subscribe_to_streams_websocket_failure(self, stream_service, mock_websocket_manager, sample_subscription):
        """Test stream subscription with WebSocket failure."""
        mock_websocket_manager.subscribe_to_streams.return_value = False
        
        result = await stream_service.subscribe_to_streams([sample_subscription])
        
        assert result is False
        # Subscription should still be stored even if WebSocket fails
        assert len(stream_service._subscriptions) == 1
    
    @pytest.mark.asyncio
    async def test_unsubscribe_from_streams_success(self, stream_service, mock_websocket_manager):
        """Test successful stream unsubscription."""
        # Add a subscription first
        stream_name = "btcusdt@kline_1m"
        stream_service._subscriptions[stream_name] = StreamSubscription("BTCUSDT", "1m")
        
        mock_websocket_manager.unsubscribe_from_streams.return_value = True
        
        result = await stream_service.unsubscribe_from_streams([stream_name])
        
        assert result is True
        assert len(stream_service._subscriptions) == 0
        mock_websocket_manager.unsubscribe_from_streams.assert_called_once_with([stream_name])
    
    @pytest.mark.asyncio
    async def test_unsubscribe_from_streams_empty(self, stream_service):
        """Test unsubscribing from empty stream list."""
        result = await stream_service.unsubscribe_from_streams([])
        assert result is True
    
    def test_add_message_handler(self, stream_service):
        """Test adding custom message handler."""
        handler = MagicMock()
        
        stream_service.add_message_handler(handler)
        
        assert len(stream_service._message_handlers) == 1
        assert handler in stream_service._message_handlers
    
    def test_add_kline_handler(self, stream_service):
        """Test adding custom K-line handler."""
        handler = MagicMock()
        
        stream_service.add_kline_handler(handler)
        
        assert len(stream_service._kline_handlers) == 1
        assert handler in stream_service._kline_handlers
    
    @pytest.mark.asyncio
    async def test_handle_kline_message_success(self, stream_service, sample_websocket_message, mock_storage):
        """Test successful K-line message handling."""
        # Add handlers
        message_handler = MagicMock()
        kline_handler = MagicMock()
        stream_service.add_message_handler(message_handler)
        stream_service.add_kline_handler(kline_handler)
        
        await stream_service._handle_kline_message(sample_websocket_message)
        
        # Statistics should be updated
        assert stream_service._stats.total_messages == 1
        assert stream_service._stats.kline_messages == 1
        assert stream_service._stats.last_message_time is not None
        
        # Handlers should be called
        message_handler.assert_called_once_with(sample_websocket_message)
        kline_handler.assert_called_once()
        
        # Message should be added to buffer
        assert len(stream_service._message_buffer) == 1
    
    @pytest.mark.asyncio
    async def test_handle_kline_message_invalid_data(self, stream_service):
        """Test handling K-line message with invalid data."""
        # Create message that won't convert to K-line data
        invalid_message = WebSocketMessage(stream="test@invalid", data={})
        
        await stream_service._handle_kline_message(invalid_message)
        
        # Should update total messages but not K-line messages
        assert stream_service._stats.total_messages == 1
        assert stream_service._stats.kline_messages == 0
        assert len(stream_service._message_buffer) == 0
    
    @pytest.mark.asyncio
    async def test_handle_kline_message_handler_error(self, stream_service, sample_websocket_message):
        """Test K-line message handling with handler error."""
        # Add handler that raises exception
        error_handler = MagicMock()
        error_handler.side_effect = Exception("Handler error")
        stream_service.add_message_handler(error_handler)
        
        # Should not raise exception
        await stream_service._handle_kline_message(sample_websocket_message)
        
        # Statistics should still be updated
        assert stream_service._stats.total_messages == 1
        assert stream_service._stats.kline_messages == 1
    
    def test_group_klines_by_stream(self, stream_service, sample_kline):
        """Test grouping K-lines by stream."""
        # Create K-lines for different streams
        kline1 = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=100, L=199, o="45000.00", c="45010.00", h="45020.00", l="44990.00",
            v="10.5", n=100, x=True, q="472605.0", V="5.2", Q="234050.0", B="0"
        )
        kline2 = KlineData(
            t=1640995200000, T=1640995259999, s="ETHUSDT", i="1m",
            f=200, L=299, o="3500.00", c="3510.00", h="3520.00", l="3490.00",
            v="20.5", n=200, x=True, q="71205.0", V="10.2", Q="35602.0", B="0"
        )
        
        grouped = stream_service._group_klines_by_stream([kline1, kline2])
        
        assert len(grouped) == 2
        assert "btcusdt@kline_1m" in grouped
        assert "ethusdt@kline_1m" in grouped
        assert len(grouped["btcusdt@kline_1m"]) == 1
        assert len(grouped["ethusdt@kline_1m"]) == 1
    
    @pytest.mark.asyncio
    async def test_process_kline_group_success(self, stream_service, sample_kline, mock_storage):
        """Test successful K-line group processing."""
        stream_key = "btcusdt@kline_1m"
        klines = [sample_kline]
        
        await stream_service._process_kline_group(stream_key, klines)
        
        # Storage should be called
        mock_storage.store_klines_batch.assert_called_once_with(klines, publish=True)
        assert stream_service._stats.storage_operations == 1
    
    @pytest.mark.asyncio
    async def test_process_kline_group_with_aggregation(self, stream_service, sample_kline, mock_storage, mock_aggregator_service):
        """Test K-line group processing with aggregation."""
        stream_key = "btcusdt@kline_1m"
        
        # Add subscription with aggregation
        subscription = StreamSubscription(
            symbol="BTCUSDT",
            interval="1m",
            auto_aggregate=True,
            target_intervals=["5m"]
        )
        stream_service._subscriptions[stream_key] = subscription
        
        # Mock aggregation result
        from kline_processor.services.kline_aggregator_service import AggregationResult
        mock_result = AggregationResult(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            klines_processed=1,
            klines_generated=1,
            time_range=(1640995200000, 1640995259999),
            duration_seconds=0.1,
            success=True
        )
        mock_aggregator_service.aggregate_klines.return_value = mock_result
        
        await stream_service._process_kline_group(stream_key, [sample_kline])
        
        # Storage should be called
        mock_storage.store_klines_batch.assert_called_once()
        
        # Aggregation should be called
        mock_aggregator_service.aggregate_klines.assert_called_once()
        assert stream_service._stats.aggregated_klines == 1
    
    @pytest.mark.asyncio
    async def test_process_kline_group_storage_error(self, stream_service, sample_kline, mock_storage):
        """Test K-line group processing with storage error."""
        mock_storage.store_klines_batch.side_effect = Exception("Storage error")
        
        stream_key = "btcusdt@kline_1m"
        await stream_service._process_kline_group(stream_key, [sample_kline])
        
        # Failed operations should be incremented
        assert stream_service._stats.failed_operations == 1
    
    @pytest.mark.asyncio
    async def test_flush_buffer_success(self, stream_service, sample_kline, mock_storage):
        """Test successful buffer flush."""
        # Add K-lines to buffer
        stream_service._message_buffer = [sample_kline, sample_kline]
        
        await stream_service._flush_buffer()
        
        # Buffer should be empty
        assert len(stream_service._message_buffer) == 0
        
        # Statistics should be updated
        assert stream_service._stats.processed_klines == 2
        
        # Storage should be called
        mock_storage.store_klines_batch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_flush_buffer_empty(self, stream_service, mock_storage):
        """Test flushing empty buffer."""
        await stream_service._flush_buffer()
        
        # Storage should not be called
        mock_storage.store_klines_batch.assert_not_called()
        assert stream_service._stats.processed_klines == 0
    
    def test_is_running_property(self, stream_service):
        """Test is_running property."""
        assert not stream_service.is_running
        
        stream_service._is_running = True
        assert stream_service.is_running
    
    def test_is_connected_property(self, stream_service, mock_websocket_manager):
        """Test is_connected property."""
        mock_websocket_manager.is_connected = True
        assert stream_service.is_connected
        
        mock_websocket_manager.is_connected = False
        assert not stream_service.is_connected
    
    def test_connection_state_property(self, stream_service, mock_websocket_manager):
        """Test connection_state property."""
        mock_websocket_manager.connection_state = ConnectionState.CONNECTED
        assert stream_service.connection_state == ConnectionState.CONNECTED
        
        mock_websocket_manager.connection_state = ConnectionState.DISCONNECTED
        assert stream_service.connection_state == ConnectionState.DISCONNECTED
    
    def test_get_subscriptions(self, stream_service):
        """Test getting current subscriptions."""
        # Add a subscription
        stream_key = "btcusdt@kline_1m"
        subscription = StreamSubscription("BTCUSDT", "1m")
        stream_service._subscriptions[stream_key] = subscription
        
        subscriptions = stream_service.get_subscriptions()
        
        assert len(subscriptions) == 1
        assert stream_key in subscriptions
        # Should return a copy, not the original
        assert subscriptions is not stream_service._subscriptions
    
    def test_get_statistics(self, stream_service, mock_websocket_manager, mock_aggregator_service):
        """Test getting service statistics."""
        # Set up some test data
        stream_service._is_running = True
        stream_service._stats.total_messages = 100
        stream_service._stats.kline_messages = 95
        stream_service._stats.processed_klines = 90
        stream_service._stats.aggregated_klines = 20
        stream_service._stats.storage_operations = 10
        stream_service._stats.failed_operations = 5
        stream_service._stats.uptime_seconds = 300.5
        stream_service._stats.last_message_time = datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        stream_service._message_buffer = [MagicMock(), MagicMock()]
        stream_service._subscriptions["test"] = StreamSubscription("TEST", "1m")
        
        stats = stream_service.get_statistics()
        
        assert stats['is_running'] is True
        assert stats['is_connected'] is True
        assert stats['connection_state'] == ConnectionState.CONNECTED.value
        assert stats['total_messages'] == 100
        assert stats['kline_messages'] == 95
        assert stats['processed_klines'] == 90
        assert stats['aggregated_klines'] == 20
        assert stats['storage_operations'] == 10
        assert stats['failed_operations'] == 5
        assert stats['uptime_seconds'] == 300.5
        assert stats['last_message_time'] == "2022-01-01T12:00:00+00:00"
        assert stats['active_subscriptions'] == 1
        assert stats['buffer_size'] == 2
        assert stats['buffer_capacity'] == 5
        assert 'websocket_stats' in stats
        assert 'aggregator_stats' in stats
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, stream_service, mock_websocket_manager):
        """Test health check when service is healthy."""
        # Set up healthy state
        stream_service._is_running = True
        mock_websocket_manager.is_connected = True
        stream_service._subscriptions["test"] = StreamSubscription("TEST", "1m")
        stream_service._stats.last_message_time = datetime.now(timezone.utc)
        stream_service._stats.total_messages = 100
        stream_service._stats.failed_operations = 5  # 5% failure rate
        
        health = await stream_service.health_check()
        
        assert health['healthy'] is True
        assert len(health['issues']) == 0
        assert health['service_running'] is True
        assert health['websocket_connected'] is True
        assert health['active_subscriptions'] == 1
        assert health['recent_activity'] is True
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy_not_running(self, stream_service):
        """Test health check when service is not running."""
        stream_service._is_running = False
        
        health = await stream_service.health_check()
        
        assert health['healthy'] is False
        assert 'Service not running' in health['issues']
        assert health['service_running'] is False
    
    @pytest.mark.asyncio 
    async def test_health_check_unhealthy_disconnected(self, stream_service, mock_websocket_manager):
        """Test health check when WebSocket is disconnected but has subscriptions."""
        stream_service._is_running = True
        mock_websocket_manager.is_connected = False
        stream_service._subscriptions["test"] = StreamSubscription("TEST", "1m")
        
        health = await stream_service.health_check()
        
        assert health['healthy'] is False
        assert 'WebSocket not connected but has subscriptions' in health['issues']
        assert health['websocket_connected'] is False
    
    @pytest.mark.asyncio
    async def test_health_check_high_failure_rate(self, stream_service, mock_websocket_manager):
        """Test health check with high failure rate."""
        stream_service._is_running = True
        mock_websocket_manager.is_connected = True
        stream_service._stats.total_messages = 100
        stream_service._stats.failed_operations = 20  # 20% failure rate
        
        health = await stream_service.health_check()
        
        assert health['healthy'] is False
        assert any('High failure rate' in issue for issue in health['issues'])
    
    @pytest.mark.asyncio
    async def test_buffer_auto_flush_on_full(self, stream_service, sample_kline, mock_storage):
        """Test buffer automatically flushes when full."""
        # Set buffer size to 2 for testing
        stream_service.buffer_size = 2
        
        # Create WebSocket message that converts to K-line
        sample_message = WebSocketMessage(
            stream="btcusdt@kline_1m",
            data={
                "e": "kline",
                "s": "BTCUSDT",
                "k": {
                    "t": 1640995200000, "T": 1640995259999, "s": "BTCUSDT", "i": "1m",
                    "f": 100, "L": 199, "o": "45000.00", "c": "45010.00", 
                    "h": "45020.00", "l": "44990.00", "v": "10.5", "n": 100, 
                    "x": True, "q": "472605.0", "V": "5.2", "Q": "234050.0", "B": "0"
                }
            }
        )
        
        # Handle messages to fill buffer
        await stream_service._handle_kline_message(sample_message)
        assert len(stream_service._message_buffer) == 1
        
        await stream_service._handle_kline_message(sample_message)
        # Buffer should be flushed automatically when it reaches capacity
        assert len(stream_service._message_buffer) == 0
        
        # Storage should have been called
        mock_storage.store_klines_batch.assert_called()
        assert stream_service._stats.processed_klines == 2
"""
K-line data HTTP endpoints.
"""

from fastapi import APIRouter, Depends, Query, HTTPException
from typing import List, Optional
import time

from ..dependencies import get_storage
from ..models.kline import KlineQueryParams, KlineResponse, KlineData
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.utils.logger import get_logger
from kline_processor.utils.converter import DataConverter

router = APIRouter()
logger = get_logger("api.klines")


@router.get("/klines", response_model=KlineResponse)
async def get_klines(
    symbol: str = Query(..., description="Trading pair symbol"),
    interval: str = Query(..., description="K-line interval"),
    start_time: Optional[int] = Query(None, description="Start timestamp (ms)"),
    end_time: Optional[int] = Query(None, description="End timestamp (ms)"),
    limit: int = Query(500, ge=1, le=1000, description="Max records"),
    storage: KlineStorage = Depends(get_storage)
) -> KlineResponse:
    """
    Retrieve historical K-line data.
    
    Returns K-line data for the specified symbol and interval within the given time range.
    If no time range is specified, returns the most recent K-lines up to the limit.
    """
    
    try:
        # Validate parameters
        params = KlineQueryParams(
            symbol=symbol,
            interval=interval,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
        
        # Set default time range if not provided
        if not params.end_time:
            params.end_time = int(time.time() * 1000)
        
        if not params.start_time:
            # Default to last 24 hours
            params.start_time = params.end_time - (24 * 60 * 60 * 1000)
        
        logger.info(f"Fetching K-lines: {params.symbol}@{params.interval} "
                   f"from {params.start_time} to {params.end_time}")
        
        # Fetch K-lines from storage
        klines = await storage.get_klines(
            symbol=params.symbol,
            interval=params.interval,
            start_time=params.start_time,
            end_time=params.end_time,
            limit=params.limit
        )
        
        # klines are KlineData objects; convert to API model list
        data_models = [KlineData(**DataConverter.format_for_storage(k)) for k in klines]
        
        return KlineResponse(
            success=True,
            data=data_models,
            count=len(data_models)
        )
        
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error fetching K-lines: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/klines/{symbol}/{interval}", response_model=KlineResponse)
async def get_klines_by_path(
    symbol: str,
    interval: str,
    start_time: Optional[int] = Query(None),
    end_time: Optional[int] = Query(None),
    limit: int = Query(500, ge=1, le=1000),
    storage: KlineStorage = Depends(get_storage)
) -> KlineResponse:
    """
    Alternative endpoint with path parameters for symbol and interval.
    """
    return await get_klines(
        symbol=symbol,
        interval=interval,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
        storage=storage
    )


@router.get("/klines/latest/{symbol}/{interval}", response_model=KlineData)
async def get_latest_kline(
    symbol: str,
    interval: str,
    storage: KlineStorage = Depends(get_storage)
) -> KlineData:
    """
    Get the latest K-line for a symbol and interval.
    """
    try:
        latest = await storage.get_latest_kline(symbol.upper(), interval)
        if not latest:
            raise HTTPException(status_code=404, detail="No K-line data found")
        
        # Convert to API format
        storage_format = DataConverter.format_for_storage(latest)
        return KlineData(**storage_format)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching latest K-line: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
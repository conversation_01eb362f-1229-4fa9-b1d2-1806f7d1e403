"""
K-line processor main entry point.

This module provides the main entry point for the K-line processor application.
It automatically starts the WebSocket streaming service with configuration from .env file.
"""

import asyncio
import sys
import signal
from pathlib import Path

from .main import KlineProcessor
from .services.websocket_stream_service import StreamSubscription
from .config.settings import Settings
from .utils.logger import get_logger


async def main():
    """Main application entry point."""
    logger = get_logger("main")
    
    try:
        # Load settings from .env
        settings = Settings()
        
        logger.info(f"🚀 Starting K-line processor with settings:")
        logger.info(f"  📊 WebSocket intervals: {', '.join(settings.websocket_intervals_list)}")
        logger.info(f"  📈 Symbols: {', '.join(settings.symbols_list)}")
        logger.info(f"  📁 Stats output: {settings.stats_output_file}")
        
        # Start the application
        async with KlineProcessor(settings) as processor:
            # Create stream subscriptions for all symbols and intervals
            logger.info(f"📡 Creating subscriptions for {len(settings.symbols_list)} symbols × {len(settings.websocket_intervals_list)} intervals...")
            subscriptions = await processor.create_all_interval_subscriptions(
                settings.symbols_list,
                settings.websocket_intervals_list
            )
            logger.info(f"🔗 Starting WebSocket streams for {len(subscriptions)} subscriptions...")
            
            # Subscribe to WebSocket streams (subscribe but gate processing by default)
            success = await processor.subscribe_to_streams(subscriptions)
            if not success:
                logger.error("❌ Failed to subscribe to WebSocket streams")
                sys.exit(1)

            # Perform startup HTTP sync (TypeScript style)
            if settings.startup_backfill_enabled:
                logger.info("📥 Starting HTTP K-line synchronization...")
                try:
                    # Sync historical data for all symbols and WebSocket intervals
                    logger.info(f"📊 Will sync {len(settings.websocket_intervals_list)} intervals: {', '.join(settings.websocket_intervals_list)}")
                    
                    summary = await processor.sync_historical_data(
                        settings.symbols_list,
                        settings.websocket_intervals_list
                    )
                    
                    logger.info(f"HTTP synchronization completed:")
                    logger.info(f"  ✅ Success: {len(summary.get('success', []))}")
                    logger.info(f"  ⏭️  Skipped: {len(summary.get('skipped', []))} (already have >50 klines)")
                    logger.info(f"  ❌ Failed: {len(summary.get('failed', []))}")
                    
                    if summary.get('failed'):
                        logger.warning(f"Failed pairs: {summary.get('failed')}")
                            
                except Exception as e:
                    logger.error(f"Startup HTTP synchronization failed: {e}")
            
            # Enable WebSocket processing for all intervals
            logger.info("🚀 Enabling WebSocket processing for all intervals...")
            try:
                await processor.enable_all_interval_processing(
                    settings.symbols_list,
                    settings.websocket_intervals_list
                )
                logger.info(f"✅ WebSocket processing enabled for {len(settings.symbols_list)} symbols × {len(settings.websocket_intervals_list)} intervals")
            except Exception as e:
                logger.warning(f"Failed to enable WebSocket processing: {e}")

            logger.info("✅ WebSocket streams started successfully")
            logger.info("📊 Real-time K-line processing is now active")
            logger.info("⏹️  Press Ctrl+C to stop")
            
            # Run the application
            await processor.run()
            
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
    except Exception as e:
        logger.error(f"❌ Application failed: {e}")
        sys.exit(1)
    finally:
        # Save final statistics if configured
        try:
            if 'processor' in locals():
                stats = processor.get_application_statistics()
                output_file = Path(settings.stats_output_file)
                output_file.parent.mkdir(parents=True, exist_ok=True)
                
                import json
                with open(output_file, 'w') as f:
                    json.dump(stats, f, indent=2, default=str)
                logger.info(f"📊 Final statistics saved to {output_file}")
        except Exception as e:
            logger.warning(f"⚠️  Failed to save statistics: {e}")


if __name__ == "__main__":
    # Handle graceful shutdown
    def signal_handler(signum, frame):
        print("\n🛑 Received interrupt signal, shutting down gracefully...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the application
    asyncio.run(main())
"""
WebSocket endpoints for real-time K-line updates.
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from typing import Dict, Optional
import json
import asyncio

from ..dependencies import get_redis
from ..websocket.connection_manager import ConnectionManager
from ..models.websocket import (
    WSMessage, WSSubscribeMessage, WSUnsubscribeMessage,
    WSDataMessage, WSErrorMessage
)
from kline_processor.storage.redis_client import RedisClient
from kline_processor.utils.logger import get_logger

router = APIRouter()
logger = get_logger("api.websocket")

# Global connection manager
manager = ConnectionManager()


@router.websocket("/klines")
async def websocket_klines(websocket: WebSocket):
    """
    WebSocket endpoint for real-time K-line updates.
    
    Protocol:
    - Subscribe: {"action": "subscribe", "symbol": "BTCUSDT", "interval": "1m"}
    - Unsubscribe: {"action": "unsubscribe", "symbol": "BTCUSDT", "interval": "1m"}
    - Data: {"type": "kline", "data": {...kline data...}}
    - Error: {"type": "error", "message": "error description"}
    """
    
    connection_id = await manager.connect(websocket)
    
    try:
        # Send welcome message
        await manager.send_json(connection_id, {
            "type": "connected",
            "message": "Connected to K-line WebSocket"
        })
        
        # Handle incoming messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                
                # Parse JSON message
                try:
                    message_data = json.loads(data)
                    message = WSMessage(**message_data)
                except json.JSONDecodeError as e:
                    await manager.send_error(connection_id, f"Invalid JSON: {e}")
                    continue
                except ValueError as e:
                    await manager.send_error(connection_id, str(e))
                    continue
                
                # Handle different actions
                if message.action == "subscribe":
                    sub_msg = WSSubscribeMessage(**message_data)
                    success = await manager.subscribe(
                        connection_id,
                        sub_msg.symbol.upper(),
                        sub_msg.interval
                    )
                    if success:
                        await manager.send_json(connection_id, {
                            "type": "subscribed",
                            "symbol": sub_msg.symbol,
                            "interval": sub_msg.interval
                        })
                    else:
                        await manager.send_error(connection_id, "Subscription failed")
                    
                elif message.action == "unsubscribe":
                    unsub_msg = WSUnsubscribeMessage(**message_data)
                    success = await manager.unsubscribe(
                        connection_id,
                        unsub_msg.symbol.upper(),
                        unsub_msg.interval
                    )
                    if success:
                        await manager.send_json(connection_id, {
                            "type": "unsubscribed",
                            "symbol": unsub_msg.symbol,
                            "interval": unsub_msg.interval
                        })
                    else:
                        await manager.send_error(connection_id, "Unsubscription failed")
                    
                elif message.action == "ping":
                    await manager.send_json(connection_id, {"type": "pong"})
                    
                else:
                    await manager.send_error(
                        connection_id,
                        f"Unknown action: {message.action}"
                    )
                    
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"WebSocket error for {connection_id}: {e}")
        await manager.send_error(connection_id, "Internal server error")
        
    finally:
        # Disconnect and cleanup
        await manager.disconnect(connection_id)
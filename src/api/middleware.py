"""
Middleware configuration for FastAPI application.

This module sets up CORS, logging, and error handling middleware.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from kline_processor.config.settings import Settings


def setup_middleware(app: FastAPI, settings: Settings):
    """
    Configure all middleware for the FastAPI application.
    
    Args:
        app: FastAPI application instance
        settings: Application settings
    """
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.api_cors_origins_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
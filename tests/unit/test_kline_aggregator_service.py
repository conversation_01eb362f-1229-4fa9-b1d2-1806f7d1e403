"""
Unit tests for K-line Aggregator Service.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from decimal import Decimal

from kline_processor.services.kline_aggregator_service import (
    KlineAggregatorService, AggregationRequest, AggregationResult
)
from kline_processor.storage.kline_storage import KlineStorage
from kline_processor.models.kline import KlineData


@pytest.fixture
def mock_storage():
    """Create mock KlineStorage."""
    mock = AsyncMock(spec=KlineStorage)
    return mock


@pytest.fixture
def aggregator_service(mock_storage):
    """Create KlineAggregatorService with mocks."""
    return KlineAggregatorService(
        storage=mock_storage,
        max_concurrent_aggregations=2,
        batch_size=100
    )


@pytest.fixture
def sample_1m_klines():
    """Create sample 1-minute K-lines for testing."""
    return [
        KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=100, L=199, o="45000.00", c="45010.00", h="45020.00", l="44990.00",
            v="10.5", n=100, x=True, q="472605.0", V="5.2", Q="234050.0", B="0"
        ),
        KlineData(
            t=1640995260000, T=1640995319999, s="BTCUSDT", i="1m",
            f=200, L=299, o="45010.00", c="45030.00", h="45040.00", l="45005.00",
            v="8.3", n=85, x=True, q="373749.0", V="4.1", Q="184575.0", B="0"
        ),
        KlineData(
            t=1640995320000, T=1640995379999, s="BTCUSDT", i="1m",
            f=300, L=399, o="45030.00", c="45015.00", h="45050.00", l="45000.00",
            v="12.7", n=120, x=True, q="571905.0", V="6.3", Q="283695.0", B="0"
        ),
        KlineData(
            t=1640995380000, T=1640995439999, s="BTCUSDT", i="1m",
            f=400, L=499, o="45015.00", c="45025.00", h="45035.00", l="45010.00",
            v="9.8", n=95, x=True, q="441245.0", V="4.9", Q="220630.0", B="0"
        ),
        KlineData(
            t=1640995440000, T=1640995499999, s="BTCUSDT", i="1m",
            f=500, L=599, o="45025.00", c="45040.00", h="45060.00", l="45020.00",
            v="15.2", n=150, x=True, q="684608.0", V="7.6", Q="342304.0", B="0"
        )
    ]


@pytest.fixture
def aggregation_request():
    """Create sample aggregation request."""
    return AggregationRequest(
        symbol="BTCUSDT",
        from_interval="1m",
        to_interval="5m",
        start_time=1640995200000,
        end_time=1640995500000,
        batch_size=100
    )


class TestKlineAggregatorService:
    """Test cases for KlineAggregatorService."""
    
    def test_initialization(self, aggregator_service):
        """Test service initialization."""
        assert aggregator_service.max_concurrent_aggregations == 2
        assert aggregator_service.batch_size == 100
        assert aggregator_service._total_aggregations == 0
        assert aggregator_service._successful_aggregations == 0
        assert aggregator_service._failed_aggregations == 0
    
    @pytest.mark.asyncio
    async def test_context_manager(self, aggregator_service):
        """Test using service as async context manager."""
        with patch.object(aggregator_service, 'close') as mock_close:
            async with aggregator_service as service:
                assert service is aggregator_service
            
            mock_close.assert_called_once()
    
    def test_validate_aggregation_request_valid(self, aggregator_service):
        """Test validation of valid aggregation request."""
        # Test valid aggregation (1m -> 5m)
        result = aggregator_service.validate_aggregation_request("1m", "5m")
        assert result is True
        
        # Test valid aggregation (1m -> 1h)
        result = aggregator_service.validate_aggregation_request("1m", "1h")
        assert result is True
    
    def test_validate_aggregation_request_invalid(self, aggregator_service):
        """Test validation of invalid aggregation request."""
        # Test invalid aggregation (5m -> 1m)
        result = aggregator_service.validate_aggregation_request("5m", "1m")
        assert result is False
        
        # Test invalid interval
        result = aggregator_service.validate_aggregation_request("invalid", "1m")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_success(self, aggregator_service, mock_storage, sample_1m_klines):
        """Test successful K-line aggregation."""
        # Mock storage to return sample data
        mock_storage.get_klines.return_value = sample_1m_klines
        mock_storage.store_klines_batch.return_value = 1
        
        result = await aggregator_service.aggregate_klines(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            start_time=1640995200000,
            end_time=1640995500000,
            store_result=True
        )
        
        assert result.success is True
        assert result.symbol == "BTCUSDT"
        assert result.from_interval == "1m"
        assert result.to_interval == "5m"
        assert result.klines_processed == 5
        assert result.klines_generated == 1  # 5 minutes = 1 five-minute period
        assert result.error is None
        
        # Verify storage was called
        mock_storage.get_klines.assert_called_once()
        mock_storage.store_klines_batch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_invalid_aggregation(self, aggregator_service):
        """Test aggregation with invalid timeframe combination."""
        result = await aggregator_service.aggregate_klines(
            symbol="BTCUSDT",
            from_interval="5m",
            to_interval="1m",  # Invalid: higher to lower
            start_time=1640995200000,
            end_time=1640995500000
        )
        
        assert result.success is False
        assert result.klines_processed == 0
        assert result.klines_generated == 0
        assert "Cannot aggregate" in result.error
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_invalid_time_range(self, aggregator_service):
        """Test aggregation with invalid time range."""
        result = await aggregator_service.aggregate_klines(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            start_time=1640995500000,
            end_time=1640995200000  # End before start
        )
        
        assert result.success is False
        assert result.klines_processed == 0
        assert result.klines_generated == 0
        assert "Invalid time range" in result.error
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_no_source_data(self, aggregator_service, mock_storage):
        """Test aggregation with no source data."""
        mock_storage.get_klines.return_value = []
        
        result = await aggregator_service.aggregate_klines(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            start_time=1640995200000,
            end_time=1640995500000
        )
        
        assert result.success is True
        assert result.klines_processed == 0
        assert result.klines_generated == 0
        assert "No source data available" in result.error
    
    @pytest.mark.asyncio
    async def test_aggregate_klines_storage_error(self, aggregator_service, mock_storage):
        """Test aggregation with storage error."""
        mock_storage.get_klines.side_effect = Exception("Storage error")
        
        result = await aggregator_service.aggregate_klines(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            start_time=1640995200000,
            end_time=1640995500000
        )
        
        assert result.success is False
        assert result.klines_processed == 0
        assert result.klines_generated == 0
        assert "Storage error" in result.error
    
    def test_group_klines_by_timeframe(self, aggregator_service, sample_1m_klines):
        """Test grouping K-lines by timeframe periods."""
        # Group 1-minute K-lines into 5-minute periods
        grouped = aggregator_service._group_klines_by_timeframe(sample_1m_klines, "5m")
        
        # Should have one group starting at aligned 5-minute boundary
        assert len(grouped) == 1
        aligned_time = 1640995200000  # Should be aligned to 5-minute boundary
        assert aligned_time in grouped
        assert len(grouped[aligned_time]) == 5  # All 5 K-lines in same period
    
    def test_aggregate_kline_group(self, aggregator_service, sample_1m_klines):
        """Test aggregating a group of K-lines."""
        # Take first 3 K-lines for testing
        klines_group = sample_1m_klines[:3]
        group_start_time = 1640995200000
        
        aggregated = aggregator_service._aggregate_kline_group(
            klines_group, "5m", group_start_time
        )
        
        assert aggregated is not None
        assert aggregated.s == "BTCUSDT"
        assert aggregated.i == "5m"
        assert aggregated.t == group_start_time
        assert aggregated.T == group_start_time + (5 * 60 * 1000) - 1
        
        # OHLC should be correct
        assert aggregated.o == "45000.00"  # First K-line open
        assert aggregated.c == "45015.00"  # Last K-line close
        assert Decimal(aggregated.h) == Decimal("45050.00")  # Highest high
        assert Decimal(aggregated.l) == Decimal("44990.00")  # Lowest low
        
        # Volumes should be summed
        expected_volume = Decimal("10.5") + Decimal("8.3") + Decimal("12.7")
        assert Decimal(aggregated.v) == expected_volume
        
        # Trade count should be summed 
        expected_trades = 100 + 85 + 120
        assert aggregated.n == expected_trades
        
        # Trade ID range
        assert aggregated.f == 100  # First trade ID
        assert aggregated.L == 399  # Last trade ID
        
        # Should be final if all source K-lines are final
        assert aggregated.x is True
    
    def test_aggregate_kline_group_empty(self, aggregator_service):
        """Test aggregating empty K-line group."""
        result = aggregator_service._aggregate_kline_group([], "5m", 1640995200000)
        assert result is None
    
    def test_aggregate_klines_batch(self, aggregator_service, sample_1m_klines):
        """Test batch aggregation of K-lines."""
        aggregated = aggregator_service._aggregate_klines_batch(sample_1m_klines, "5m")
        
        # Should produce one 5-minute K-line from five 1-minute K-lines
        assert len(aggregated) == 1
        
        kline = aggregated[0]
        assert kline.s == "BTCUSDT"
        assert kline.i == "5m"
        assert kline.o == "45000.00"  # First open
        assert kline.c == "45040.00"  # Last close
    
    def test_aggregate_klines_batch_empty(self, aggregator_service):
        """Test batch aggregation with empty input."""
        result = aggregator_service._aggregate_klines_batch([], "5m")
        assert result == []
    
    @pytest.mark.asyncio
    async def test_aggregate_multiple_symbols(self, aggregator_service, mock_storage, sample_1m_klines):
        """Test aggregating multiple symbols."""
        # Mock storage to return sample data
        mock_storage.get_klines.return_value = sample_1m_klines
        mock_storage.store_klines_batch.return_value = 1
        
        requests = [
            AggregationRequest(
                symbol="BTCUSDT",
                from_interval="1m",
                to_interval="5m",
                start_time=1640995200000,
                end_time=1640995500000
            ),
            AggregationRequest(
                symbol="ETHUSDT",
                from_interval="1m",
                to_interval="5m",
                start_time=1640995200000,
                end_time=1640995500000
            )
        ]
        
        results = await aggregator_service.aggregate_multiple_symbols(requests, store_result=True)
        
        assert len(results) == 2
        assert all(r.success for r in results)
        assert results[0].symbol == "BTCUSDT"
        assert results[1].symbol == "ETHUSDT"
        
        # Storage should be called for each symbol
        assert mock_storage.get_klines.call_count == 2
        assert mock_storage.store_klines_batch.call_count == 2
    
    @pytest.mark.asyncio
    async def test_aggregate_multiple_symbols_empty(self, aggregator_service):
        """Test aggregating empty request list."""
        results = await aggregator_service.aggregate_multiple_symbols([])
        assert results == []
    
    @pytest.mark.asyncio
    async def test_aggregate_multiple_symbols_with_error(self, aggregator_service, mock_storage, sample_1m_klines):
        """Test aggregating multiple symbols with one error."""
        # First call succeeds, second fails
        mock_storage.get_klines.side_effect = [
            [sample_1m_klines[0]],  # Success for first
            Exception("Storage error")  # Error for second
        ]
        mock_storage.store_klines_batch.return_value = 1
        
        requests = [
            AggregationRequest(
                symbol="BTCUSDT",
                from_interval="1m",
                to_interval="5m",
                start_time=1640995200000,
                end_time=1640995500000
            ),
            AggregationRequest(
                symbol="ETHUSDT",
                from_interval="1m",
                to_interval="5m",
                start_time=1640995200000,
                end_time=1640995500000
            )
        ]
        
        results = await aggregator_service.aggregate_multiple_symbols(requests)
        
        assert len(results) == 2
        assert results[0].success is True
        assert results[1].success is False
        assert "Storage error" in results[1].error
    
    @pytest.mark.asyncio
    async def test_get_supported_aggregations(self, aggregator_service):
        """Test getting supported aggregation targets."""
        supported = await aggregator_service.get_supported_aggregations("1m")
        
        # Should include higher timeframes that are multiples of 1m
        assert "5m" in supported
        assert "15m" in supported
        assert "1h" in supported
        assert "1d" in supported
    
    @pytest.mark.asyncio
    async def test_get_supported_aggregations_invalid(self, aggregator_service):
        """Test getting supported aggregations for invalid interval."""
        with patch('kline_processor.utils.timeframe.TimeframeUtils.get_timeframe_hierarchy', 
                  side_effect=Exception("Error")):
            supported = await aggregator_service.get_supported_aggregations("invalid")
            assert supported == []
    
    @pytest.mark.asyncio
    async def test_get_aggregation_coverage(self, aggregator_service, mock_storage, sample_1m_klines):
        """Test getting aggregation coverage."""
        # Mock storage responses
        mock_storage.get_klines.return_value = [sample_1m_klines[0]]
        mock_storage.get_latest_kline.return_value = sample_1m_klines[-1]
        
        coverage = await aggregator_service.get_aggregation_coverage("BTCUSDT", "5m")
        
        assert coverage is not None
        assert coverage[0] == sample_1m_klines[0].t
        assert coverage[1] == sample_1m_klines[-1].t
    
    @pytest.mark.asyncio
    async def test_get_aggregation_coverage_no_data(self, aggregator_service, mock_storage):
        """Test getting aggregation coverage with no data."""
        mock_storage.get_klines.return_value = []
        
        coverage = await aggregator_service.get_aggregation_coverage("BTCUSDT", "5m")
        
        assert coverage is None
    
    @pytest.mark.asyncio
    async def test_get_aggregation_coverage_error(self, aggregator_service, mock_storage):
        """Test getting aggregation coverage with storage error."""
        mock_storage.get_klines.side_effect = Exception("Storage error")
        
        coverage = await aggregator_service.get_aggregation_coverage("BTCUSDT", "5m")
        
        assert coverage is None
    
    def test_get_statistics(self, aggregator_service):
        """Test getting service statistics."""
        # Set some test data
        aggregator_service._total_aggregations = 10
        aggregator_service._successful_aggregations = 8
        aggregator_service._failed_aggregations = 2
        aggregator_service._total_klines_processed = 1000
        aggregator_service._total_klines_generated = 200
        
        stats = aggregator_service.get_statistics()
        
        assert stats['total_aggregations'] == 10
        assert stats['successful_aggregations'] == 8
        assert stats['failed_aggregations'] == 2
        assert stats['success_rate'] == 80.0
        assert stats['total_klines_processed'] == 1000
        assert stats['total_klines_generated'] == 200
        assert stats['max_concurrent_aggregations'] == 2
        assert stats['batch_size'] == 100
    
    def test_get_statistics_no_aggregations(self, aggregator_service):
        """Test getting statistics with no aggregations."""
        stats = aggregator_service.get_statistics()
        
        assert stats['total_aggregations'] == 0
        assert stats['success_rate'] == 0.0
    
    @pytest.mark.asyncio
    async def test_close(self, aggregator_service):
        """Test closing the service."""
        # Should not raise exception
        await aggregator_service.close()


class TestAggregationRequest:
    """Test cases for AggregationRequest."""
    
    def test_aggregation_request_creation(self):
        """Test creating aggregation request."""
        request = AggregationRequest(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            start_time=1640995200000,
            end_time=1640995500000,
            batch_size=500
        )
        
        assert request.symbol == "BTCUSDT"
        assert request.from_interval == "1m"
        assert request.to_interval == "5m"
        assert request.start_time == 1640995200000
        assert request.end_time == 1640995500000
        assert request.batch_size == 500
    
    def test_aggregation_request_default_batch_size(self):
        """Test aggregation request with default batch size."""
        request = AggregationRequest(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            start_time=1640995200000,
            end_time=1640995500000
        )
        
        assert request.batch_size == 1000  # Default value


class TestAggregationResult:
    """Test cases for AggregationResult."""
    
    def test_aggregation_result_creation(self):
        """Test creating aggregation result."""
        result = AggregationResult(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            klines_processed=100,
            klines_generated=20,
            time_range=(1640995200000, 1640995500000),
            duration_seconds=1.5,
            success=True,
            error=None
        )
        
        assert result.symbol == "BTCUSDT"
        assert result.from_interval == "1m"
        assert result.to_interval == "5m"
        assert result.klines_processed == 100
        assert result.klines_generated == 20
        assert result.time_range == (1640995200000, 1640995500000)
        assert result.duration_seconds == 1.5
        assert result.success is True
        assert result.error is None
    
    def test_aggregation_result_with_error(self):
        """Test creating aggregation result with error."""
        result = AggregationResult(
            symbol="BTCUSDT",
            from_interval="1m",
            to_interval="5m",
            klines_processed=0,
            klines_generated=0,
            time_range=(1640995200000, 1640995500000),
            duration_seconds=0.1,
            success=False,
            error="Test error"
        )
        
        assert result.success is False
        assert result.error == "Test error"
"""
Unit tests for data conversion utilities.
"""

import pytest
from decimal import Decimal

from kline_processor.utils.converter import DataConverter
from kline_processor.models.kline import KlineData, ValidatedKlineData


class TestDataConverter:
    """Test cases for DataConverter class."""
    
    def test_api_response_to_kline_data_success(self):
        """Test successful conversion from API response to KlineData."""
        api_data = [
            1640995200000,      # Open time
            "45000.12",         # Open
            "45200.00",         # High
            "44950.50",         # Low
            "45123.45",         # Close
            "123.456789",       # Volume
            1640995259999,      # Close time
            "5567890.12",       # Quote asset volume
            1500,               # Number of trades
            "62.123456",        # Taker buy base asset volume
            "2784567.89",       # Taker buy quote asset volume
            "0"                 # Unused field
        ]
        
        kline = DataConverter.api_response_to_kline_data(api_data)
        assert kline is not None
        assert kline.t == 1640995200000
        assert kline.T == 1640995259999
        assert kline.o == "45000.12"
        assert kline.c == "45123.45"
        assert kline.h == "45200.00"
        assert kline.l == "44950.50"
        assert kline.v == "123.456789"
        assert kline.n == 1500
        assert kline.x is True
        assert kline.q == "5567890.12"
        assert kline.V == "62.123456"
        assert kline.Q == "2784567.89"
    
    def test_api_response_to_kline_data_invalid(self):
        """Test conversion with invalid API data."""
        # Too few elements
        assert DataConverter.api_response_to_kline_data([1, 2, 3]) is None
        
        # Empty list
        assert DataConverter.api_response_to_kline_data([]) is None
        
        # None input
        assert DataConverter.api_response_to_kline_data(None) is None
        
        # Invalid data types
        invalid_data = ["invalid", "data", "types", "everywhere", 
                       "45000.12", "123.45", "more", "invalid", "data", "here", "too"]
        assert DataConverter.api_response_to_kline_data(invalid_data) is None
    
    def test_kline_data_to_api_format(self):
        """Test conversion from KlineData to API format."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        api_format = DataConverter.kline_data_to_api_format(kline)
        assert api_format[0] == 1640995200000  # Open time
        assert api_format[1] == "45000.12"     # Open
        assert api_format[2] == "45200.00"     # High
        assert api_format[3] == "44950.50"     # Low
        assert api_format[4] == "45123.45"     # Close
        assert api_format[5] == "123.456789"   # Volume
        assert api_format[6] == 1640995259999  # Close time
        assert api_format[7] == "5567890.12"   # Quote volume
        assert api_format[8] == 1500           # Number of trades
        assert api_format[9] == "62.123456"    # Taker buy volume
        assert api_format[10] == "2784567.89"  # Taker buy quote volume
        assert api_format[11] == "0"           # Unused field
    
    def test_websocket_to_kline_data_success(self):
        """Test successful conversion from WebSocket data."""
        ws_data = {
            't': 1640995200000,
            'T': 1640995259999,
            'f': 1234567890,
            'L': 1234567899,
            'o': '45000.12',
            'c': '45123.45',
            'h': '45200.00',
            'l': '44950.50',
            'v': '123.456789',
            'n': 1500,
            'x': True,
            'q': '5567890.12',
            'V': '62.123456',
            'Q': '2784567.89',
            'B': '0'
        }
        
        kline = DataConverter.websocket_to_kline_data(ws_data, "BTCUSDT", "1m")
        assert kline is not None
        assert kline.s == "BTCUSDT"
        assert kline.i == "1m"
        assert kline.t == 1640995200000
        assert kline.o == "45000.12"
        assert kline.x is True
    
    def test_websocket_to_kline_data_invalid(self):
        """Test conversion with invalid WebSocket data."""
        # Empty data
        assert DataConverter.websocket_to_kline_data({}, "BTCUSDT", "1m") is None
        
        # None input
        assert DataConverter.websocket_to_kline_data(None, "BTCUSDT", "1m") is None
        
        # Missing required fields
        incomplete_data = {'t': 1640995200000, 'o': '45000.12'}
        assert DataConverter.websocket_to_kline_data(incomplete_data, "BTCUSDT", "1m") is None
    
    def test_aggregate_klines_success(self):
        """Test successful K-line aggregation."""
        klines = [
            KlineData(
                t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
                f=1000, L=1100, o="45000.00", c="45010.00", h="45020.00", l="45000.00",
                v="10.0", n=100, x=True, q="450100.00", V="5.0", Q="225050.00", B="0"
            ),
            KlineData(
                t=1640995260000, T=1640995319999, s="BTCUSDT", i="1m",
                f=1101, L=1200, o="45010.00", c="45030.00", h="45040.00", l="45005.00",
                v="12.0", n=120, x=True, q="540360.00", V="6.0", Q="270180.00", B="0"
            ),
            KlineData(
                t=1640995320000, T=1640995379999, s="BTCUSDT", i="1m",
                f=1201, L=1300, o="45030.00", c="45020.00", h="45050.00", l="45010.00",
                v="8.0", n=80, x=True, q="360160.00", V="4.0", Q="180080.00", B="0"
            )
        ]
        
        aggregated = DataConverter.aggregate_klines(klines, "3m")
        assert aggregated is not None
        assert aggregated.i == "3m"
        assert aggregated.s == "BTCUSDT"
        assert aggregated.t == 1640995200000  # First open time
        assert aggregated.T == 1640995379999  # Last close time
        assert aggregated.o == "45000.00"     # First open
        assert aggregated.c == "45020.00"     # Last close
        assert aggregated.h == "45050.00"     # Highest high
        assert aggregated.l == "45000.00"     # Lowest low
        assert Decimal(aggregated.v) == Decimal("30.0")  # Sum of volumes
        assert aggregated.n == 300            # Sum of trades
        assert aggregated.f == 1000           # First trade ID
        assert aggregated.L == 1300           # Last trade ID
    
    def test_aggregate_klines_empty(self):
        """Test aggregation with empty list."""
        assert DataConverter.aggregate_klines([], "5m") is None
    
    def test_update_kline_incremental_success(self):
        """Test successful incremental K-line update."""
        base_kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45100.00", h="45150.00", l="44950.50",
            v="123.456789", n=1500, x=False, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        updates = {
            'c': '45123.45',
            'h': '45200.00',
            'v': '125.456789',
            'n': 1520,
            'x': True
        }
        
        updated = DataConverter.update_kline_incremental(base_kline, updates)
        assert updated is not None
        assert updated.c == "45123.45"
        assert updated.h == "45200.00"
        assert updated.v == "125.456789"
        assert updated.n == 1520
        assert updated.x is True
        # Unchanged fields should remain the same
        assert updated.o == "45000.12"
        assert updated.s == "BTCUSDT"
    
    def test_update_kline_incremental_invalid(self):
        """Test incremental update with invalid inputs."""
        base_kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45100.00", h="45150.00", l="44950.50",
            v="123.456789", n=1500, x=False, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        # None inputs
        assert DataConverter.update_kline_incremental(None, {}) is None
        assert DataConverter.update_kline_incremental(base_kline, None) is None
        
        # Empty updates
        assert DataConverter.update_kline_incremental(base_kline, {}) is None
    
    def test_normalize_price_precision(self):
        """Test price precision normalization."""
        assert DataConverter.normalize_price_precision("45000.123456789", 2) == "45000.12"
        assert DataConverter.normalize_price_precision("45000", 2) == "45000.00"
        assert DataConverter.normalize_price_precision("45000.999", 1) == "45001.0"
        
        # Invalid inputs should return original
        assert DataConverter.normalize_price_precision("invalid", 2) == "invalid"
        assert DataConverter.normalize_price_precision(None, 2) is None
    
    def test_normalize_volume_precision(self):
        """Test volume precision normalization."""
        assert DataConverter.normalize_volume_precision("123.456789012", 6) == "123.456789"
        assert DataConverter.normalize_volume_precision("123", 4) == "123.0000"
        assert DataConverter.normalize_volume_precision("123.999999", 2) == "124.00"
        
        # Invalid inputs should return original
        assert DataConverter.normalize_volume_precision("invalid", 4) == "invalid"
        assert DataConverter.normalize_volume_precision(None, 4) is None
    
    def test_validate_kline_consistency_valid(self):
        """Test validation with consistent K-line data."""
        valid_kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        assert DataConverter.validate_kline_consistency(valid_kline) is True
    
    def test_validate_kline_consistency_invalid(self):
        """Test validation with inconsistent K-line data."""
        # Invalid timestamps (close before open)
        invalid_timestamps = KlineData(
            t=1640995259999, T=1640995200000, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        assert DataConverter.validate_kline_consistency(invalid_timestamps) is False
        
        # Invalid OHLC (high lower than open)
        invalid_ohlc = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="44000.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        assert DataConverter.validate_kline_consistency(invalid_ohlc) is False
        
        # Negative volume
        negative_volume = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="-123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        assert DataConverter.validate_kline_consistency(negative_volume) is False
    
    def test_convert_to_validated_kline_success(self):
        """Test successful conversion to ValidatedKlineData."""
        valid_kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        validated = DataConverter.convert_to_validated_kline(valid_kline)
        assert validated is not None
        assert isinstance(validated, ValidatedKlineData)
        assert validated.s == "BTCUSDT"
        assert validated.t == 1640995200000
    
    def test_convert_to_validated_kline_invalid(self):
        """Test conversion with invalid K-line data."""
        invalid_kline = KlineData(
            t=1640995259999, T=1640995200000, s="BTCUSDT", i="1m",  # Invalid timestamps
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        validated = DataConverter.convert_to_validated_kline(invalid_kline)
        assert validated is None
    
    def test_format_for_storage(self):
        """Test formatting K-line data for storage."""
        kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        storage_dict = DataConverter.format_for_storage(kline)
        assert storage_dict['ot'] == 1640995200000  # open_time
        assert storage_dict['ct'] == 1640995259999  # close_time
        assert storage_dict['s'] == "BTCUSDT"       # symbol
        assert storage_dict['i'] == "1m"            # interval
        assert storage_dict['o'] == "45000.12"      # open
        assert storage_dict['c'] == "45123.45"      # close
        assert storage_dict['h'] == "45200.00"      # high
        assert storage_dict['l'] == "44950.50"      # low
        assert storage_dict['v'] == "123.456789"    # volume
        assert storage_dict['n'] == 1500            # num_trades
        assert storage_dict['x'] is True            # is_closed
        assert storage_dict['bv'] == "62.123456"    # taker_buy_volume
    
    def test_parse_from_storage_success(self):
        """Test successful parsing from storage format."""
        storage_data = {
            'ot': 1640995200000,
            'ct': 1640995259999,
            's': 'BTCUSDT',
            'i': '1m',
            'ft': 1234567890,
            'lt': 1234567899,
            'o': '45000.12',
            'c': '45123.45',
            'h': '45200.00',
            'l': '44950.50',
            'v': '123.456789',
            'n': 1500,
            'x': True,
            'q': '5567890.12',
            'bv': '62.123456',
            'bq': '2784567.89'
        }
        
        kline = DataConverter.parse_from_storage(storage_data)
        assert kline is not None
        assert kline.t == 1640995200000
        assert kline.s == "BTCUSDT"
        assert kline.o == "45000.12"
        assert kline.x is True
    
    def test_parse_from_storage_invalid(self):
        """Test parsing with invalid storage data."""
        # Missing required fields
        incomplete_data = {'ot': 1640995200000, 's': 'BTCUSDT'}
        assert DataConverter.parse_from_storage(incomplete_data) is None
        
        # Invalid data types
        invalid_data = {
            'ot': 'invalid_timestamp',
            'ct': 1640995259999,
            's': 'BTCUSDT',
            'i': '1m'
        }
        assert DataConverter.parse_from_storage(invalid_data) is None
    
    def test_calculate_price_change(self):
        """Test price change calculation."""
        changes = DataConverter.calculate_price_change("45000.00", "45123.45")
        assert changes['absolute'] == "123.45"
        assert float(changes['percentage']) > 0.27  # Approximate percentage
        
        # No change
        no_change = DataConverter.calculate_price_change("45000.00", "45000.00")
        assert no_change['absolute'] == "0"
        assert no_change['percentage'] == "0.000000"
        
        # Negative change
        negative = DataConverter.calculate_price_change("45123.45", "45000.00")
        assert negative['absolute'] == "-123.45"
        assert float(negative['percentage']) < 0
        
        # Division by zero
        div_zero = DataConverter.calculate_price_change("0", "100")
        assert div_zero['percentage'] == "0.000000"
        
        # Invalid inputs
        invalid = DataConverter.calculate_price_change("invalid", "45000.00")
        assert invalid['absolute'] == "0"
        assert invalid['percentage'] == "0"
    
    def test_round_trip_conversions(self):
        """Test round-trip conversions maintain data integrity."""
        original_kline = KlineData(
            t=1640995200000, T=1640995259999, s="BTCUSDT", i="1m",
            f=1234567890, L=1234567899, o="45000.12", c="45123.45", h="45200.00", l="44950.50",
            v="123.456789", n=1500, x=True, q="5567890.12", V="62.123456", Q="2784567.89", B="0"
        )
        
        # KlineData -> API format -> KlineData
        api_format = DataConverter.kline_data_to_api_format(original_kline)
        converted_kline = DataConverter.api_response_to_kline_data(api_format)
        
        assert converted_kline is not None
        assert converted_kline.t == original_kline.t
        assert converted_kline.o == original_kline.o
        assert converted_kline.c == original_kline.c
        assert converted_kline.h == original_kline.h
        assert converted_kline.l == original_kline.l
        
        # KlineData -> Storage -> KlineData
        storage_format = DataConverter.format_for_storage(original_kline)
        parsed_kline = DataConverter.parse_from_storage(storage_format)
        
        assert parsed_kline is not None
        assert parsed_kline.t == original_kline.t
        assert parsed_kline.s == original_kline.s
        assert parsed_kline.i == original_kline.i
        assert parsed_kline.x == original_kline.x
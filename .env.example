# Exchange API Configuration
KLINE_URL_V2=https://fapi.binance.com/fapi/v1/continuousKlines
BASE_URL=wss://fstream.binance.com

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=3

# Trading Configuration
TIMEFRAMES=1m,3m,5m,15m,30m,1h,2h,4h,6h,8h,12h,1d,3d,1w,1M
SYMBOLS=BTCUSDT,ETHUSDT

# WebSocket Configuration
WEBSOCKET_CHUNK_SIZE=20
MAX_RETRIES=5
# Stream buffer flush interval in milliseconds
# Lower values improve latency but increase write frequency. Typical: 200-2000
STREAM_FLUSH_INTERVAL_MS=1000

# Logging Configuration
LOG_LEVEL=INFO
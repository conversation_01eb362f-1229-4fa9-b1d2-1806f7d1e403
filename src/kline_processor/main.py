"""
Main application entry point for K-line processor.

This module provides the main application class that integrates all services
and provides a unified interface for K-line data processing.
"""

import asyncio
import signal
from typing import Optional, List, Dict, Any

from .config.settings import Settings
from .services.historical_data_service import HistoricalDataService
from .services.websocket_stream_service import WebSocketStreamService, StreamSubscription
from .storage.kline_storage import KlineStorage
from .websocket.websocket_manager import WebSocketManager
from .clients.exchange_api_client_v2 import ExchangeAPIClientV2
from .storage.redis_client import RedisClient
from .utils.logger import get_logger
from .models.kline import KlineData


class KlineProcessor:
    """
    Main K-line processor application.
    
    This class integrates all services and provides a unified interface
    for K-line data processing including historical data fetching,
    real-time streaming, and timeframe aggregation.
    """
    
    def __init__(self, settings: Optional[Settings] = None, setup_signals: bool = True):
        """
        Initialize K-line processor application.
        
        Args:
            settings: Application settings (defaults to global settings)
            setup_signals: Whether to setup signal handlers (default True)
        """
        self.settings = settings or Settings()
        self.logger = get_logger(self.__class__.__name__)
        
        # Core components
        self._redis_client: Optional[RedisClient] = None
        self._storage: Optional[KlineStorage] = None
        self._api_client: Optional[ExchangeAPIClientV2] = None
        self._websocket_manager: Optional[WebSocketManager] = None
        
        # Services
        self._historical_service: Optional[HistoricalDataService] = None
        self._stream_service: Optional[WebSocketStreamService] = None
        
        # Application state
        self._is_running = False
        self._shutdown_event = asyncio.Event()
        
        # Setup signal handlers only if requested
        if setup_signals:
            self._setup_signal_handlers()
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating shutdown...")
            # Set the shutdown event
            self._shutdown_event.set()
            # Also raise KeyboardInterrupt to break any blocking operations
            if signum == signal.SIGINT:
                raise KeyboardInterrupt()
        
        try:
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        except Exception as e:
            self.logger.warning(f"Could not setup signal handlers: {e}")
    
    async def initialize(self):
        """Initialize all components and services."""
        try:
            self.logger.info("Initializing K-line processor application...")
            
            # Initialize core components
            self._redis_client = RedisClient()
            await self._redis_client.connect()
            
            self._storage = KlineStorage(redis_client=self._redis_client)
            
            self._api_client = ExchangeAPIClientV2()
            
            self._websocket_manager = WebSocketManager(
                base_url=self.settings.websocket_base_url
            )
            
            # Initialize services
            self._historical_service = HistoricalDataService(
                exchange_client=self._api_client,
                storage=self._storage,
                settings=self.settings
            )
            
            self._stream_service = WebSocketStreamService()
            
            self.logger.info("K-line processor application initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def start(self):
        """Start the application and all services."""
        if self._is_running:
            self.logger.warning("Application is already running")
            return
        
        try:
            # Initialize components if not already done
            if not self._redis_client:
                await self.initialize()
            
            # Start services that have start methods
            if hasattr(self._stream_service, 'start'):
                await self._stream_service.start()
            
            self._is_running = True
            self.logger.info("K-line processor application started successfully")
            
        except Exception as e:
            self._is_running = False
            self.logger.error(f"Failed to start application: {e}")
            raise
    
    async def stop(self):
        """Stop the application and cleanup resources."""
        if not self._is_running:
            return
        
        # Mark as not running immediately to prevent multiple stop calls
        self._is_running = False
        
        try:
            self.logger.info("Stopping K-line processor application...")
            
            # Create shutdown tasks with timeout
            shutdown_tasks = []
            
            # Stop services
            if self._stream_service and hasattr(self._stream_service, 'stop'):
                shutdown_tasks.append(self._stream_service.stop())
            
            # Close core components
            if self._websocket_manager:
                shutdown_tasks.append(self._websocket_manager.disconnect())
            
            if self._api_client:
                shutdown_tasks.append(self._api_client.close())
            
            if self._redis_client:
                shutdown_tasks.append(self._redis_client.close())
            
            # Wait for all shutdown tasks with timeout
            if shutdown_tasks:
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*shutdown_tasks, return_exceptions=True),
                        timeout=5.0  # 5 second timeout
                    )
                except asyncio.TimeoutError:
                    self.logger.warning("Some services did not shutdown cleanly within timeout")
            
            self.logger.info("K-line processor application stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping application: {e}")
    
    async def run(self):
        """Run the application until shutdown signal received."""
        try:
            await self.start()
            
            self.logger.info("Application is running. Press Ctrl+C to stop.")
            
            # Wait for shutdown signal
            try:
                await self._shutdown_event.wait()
            except asyncio.CancelledError:
                self.logger.info("Run task cancelled")
            
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            raise
        finally:
            # Ensure we only stop once
            if self._is_running:
                await self.stop()
    
    # Service access methods
    
    async def fetch_historical_data(self,
                                  symbol: str,
                                  interval: str,
                                  start_time: Optional[int] = None,
                                  end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        Fetch historical K-line data.
        
        Args:
            symbol: Trading symbol
            interval: K-line interval
            start_time: Start timestamp (ms)
            end_time: End timestamp (ms)
            
        Returns:
            Fetch operation result
        """
        if not self._historical_service:
            raise RuntimeError("Historical service not initialized")
        
        if start_time is None or end_time is None:
            # If no time range specified, get recent data (last hour)
            import time
            end_time = int(time.time() * 1000)
            start_time = end_time - (60 * 60 * 1000)  # 1 hour ago
        
        result = await self._historical_service.fetch_historical_data(
            symbol=symbol,
            interval=interval,
            start_time=start_time,
            end_time=end_time
        )
        
        # Convert FetchResult to dict for JSON serialization
        return {
            'success': result.success,
            'symbol': result.symbol,
            'interval': result.interval,
            'klines_fetched': result.klines_fetched,
            'time_range': result.time_range,
            'gaps_filled': result.gaps_filled,
            'duration_seconds': result.duration_seconds,
            'error': result.error
        }
    
    async def subscribe_to_streams(self, subscriptions: List[StreamSubscription]) -> bool:
        """
        Subscribe to real-time K-line streams.
        
        Args:
            subscriptions: List of stream subscriptions
            
        Returns:
            True if subscription successful
        """
        if not self._stream_service:
            raise RuntimeError("Stream service not initialized")
        
        return await self._stream_service.subscribe_to_streams(subscriptions)
    
    async def create_all_interval_subscriptions(self, symbols: List[str], intervals: List[str]) -> List[StreamSubscription]:
        """
        Create WebSocket subscriptions for all symbol/interval combinations.
        
        This replaces the aggregation-based approach with direct subscriptions
        for each timeframe.
        
        Args:
            symbols: List of trading symbols
            intervals: List of intervals to subscribe to
            
        Returns:
            List of stream subscriptions
        """
        subscriptions = []
        
        for symbol in symbols:
            for interval in intervals:
                subscription = StreamSubscription(
                    symbol=symbol,
                    interval=interval,
                    auto_aggregate=False,  # Disable aggregation
                    target_intervals=[]    # No aggregation targets
                )
                subscriptions.append(subscription)
        
        self.logger.info(f"Created {len(subscriptions)} subscriptions for {len(symbols)} symbols and {len(intervals)} intervals")
        return subscriptions

    async def sync_historical_data(self, symbols: List[str], intervals: List[str]) -> Dict[str, Any]:
        """
        Sync historical data for symbols and intervals matching TypeScript logic.
        
        Args:
            symbols: List of symbols to sync
            intervals: List of intervals to sync
            
        Returns:
            Summary of sync results
        """
        try:
            # Initialize Redis if needed
            if not self._redis_client:
                self._redis_client = RedisClient()
                await self._redis_client.connect()
            
            # Clear any existing sync flags
            await self._redis_client.del_keys_by_pattern("kline:sync:done:*")
            await self._redis_client.del_keys_by_pattern("kline:ws:enabled:*")
            
            # Get target count and iterations from settings
            target_count = self.settings.startup_backfill_target_klines
            iterations = self.settings.startup_backfill_iterations
            
            # Sync K-lines for all symbols and intervals with rate limiting
            all_results = []
            for symbol in symbols:
                for interval in intervals:
                    self.logger.info(f"Syncing {symbol}:{interval}")
                    result = await self._historical_service.sync_klines(
                        symbol=symbol,
                        interval=interval,
                        target_count=target_count,
                        count=iterations
                    )
                    all_results.append(result)
                    
                    # Add delay between requests to avoid rate limits
                    await asyncio.sleep(0.5)
            
            # Prepare summary
            summary = {
                'success': [],
                'failed': [],
                'skipped': []
            }
            
            for result in all_results:
                if result.success:
                    if result.error and "Skipped" in result.error:
                        summary['skipped'].append(f"{result.symbol}:{result.interval}")
                    else:
                        summary['success'].append(f"{result.symbol}:{result.interval}")
                        # Set sync done flag for each interval
                        await self._redis_client.set_flag(f"kline:sync:done:{result.symbol.upper()}:{result.interval}", 1)
                else:
                    summary['failed'].append(f"{result.symbol}:{result.interval}")
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Historical sync failed: {e}")
            raise

    async def enable_websocket_processing(self, symbols: List[str]) -> None:
        """
        Enable WebSocket processing for all symbols.
        
        Args:
            symbols: List of symbols to enable
        """
        try:
            if not self._redis_client:
                self._redis_client = RedisClient()
                await self._redis_client.connect()
                
            for symbol in symbols:
                # Enable processing for 1m interval (base)
                await self._stream_service.set_processing_enabled(symbol, "1m", True)
                # Also set Redis flag
                await self._redis_client.set_flag(f"kline:ws:enabled:{symbol.upper()}:1m", 1)
                self.logger.info(f"Enabled WebSocket processing for {symbol}:1m")
                
        except Exception as e:
            self.logger.error(f"Error enabling WebSocket processing: {e}")
            raise

    async def enable_all_interval_processing(self,
                                           symbols: List[str],
                                           intervals: List[str]) -> None:
        """
        Enable WebSocket processing for all symbol/interval combinations.
        
        This replaces the previous aggregation approach by enabling direct
        WebSocket subscriptions for each interval.
        
        Args:
            symbols: List of symbols to process
            intervals: List of intervals to enable (e.g., ['1m', '3m', '5m', '1h'])
        """
        try:
            if not self._redis_client:
                self._redis_client = RedisClient()
                await self._redis_client.connect()
                
            for symbol in symbols:
                for interval in intervals:
                    try:
                        # Enable WebSocket processing for this interval
                        if self._redis_client:
                            await self._redis_client.set_flag(f"kline:sync:done:{symbol.upper()}:{interval}", 1)
                            await self._redis_client.set_flag(f"kline:ws:enabled:{symbol.upper()}:{interval}", 1)
                        
                        if hasattr(self._stream_service, "set_processing_enabled"):
                            await self._stream_service.set_processing_enabled(symbol, interval, True)
                        
                        self.logger.info(f"✅ Enabled WebSocket processing for {symbol}:{interval}")
                        
                    except Exception as e:
                        self.logger.error(f"❌ Exception enabling processing for {symbol}:{interval}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error enabling interval processing: {e}")
            raise
    
    
    def get_application_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive application statistics.
        
        Returns:
            Dictionary with application statistics
        """
        stats = {
            'application': {
                'is_running': self._is_running,
                'settings': {
                    'redis_host': self.settings.redis_host,
                    'redis_port': self.settings.redis_port,
                    'redis_db': self.settings.redis_db,
                    'log_level': self.settings.log_level,
                    'base_url': self.settings.base_url,
                    'symbols': self.settings.symbols_list,
                    'aggregate_intervals': self.settings.aggregate_intervals_list
                }
            }
        }
        
        # Add service statistics
        if self._historical_service:
            stats['historical_service'] = self._historical_service.get_statistics()
        
        if self._stream_service:
            stats['stream_service'] = self._stream_service.get_statistics()
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check.
        
        Returns:
            Health check results
        """
        health = {
            'healthy': True,
            'issues': [],
            'application_running': self._is_running,
            'services': {}
        }
        
        # Check Redis connection
        if self._redis_client:
            try:
                await self._redis_client.ping()
                health['services']['redis'] = {'healthy': True}
            except Exception as e:
                health['healthy'] = False
                health['issues'].append(f'Redis connection failed: {e}')
                health['services']['redis'] = {'healthy': False, 'error': str(e)}
        
        # Check services
        if self._stream_service:
            stream_health = await self._stream_service.health_check()
            health['services']['stream'] = stream_health
            if not stream_health['healthy']:
                health['healthy'] = False
                health['issues'].extend(stream_health['issues'])
        
        return health
    
    @property
    def is_running(self) -> bool:
        """Check if application is running."""
        return self._is_running
    
    @property
    def settings(self) -> Settings:
        """Get application settings."""
        return self._settings
    
    @settings.setter
    def settings(self, value: Settings):
        """Set application settings."""
        self._settings = value


# Global application instance
app = KlineProcessor()


# Application entry point has moved to __main__.py
# This file now only contains the core KlineProcessor class
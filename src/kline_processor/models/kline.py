"""
K-line data models with comprehensive validation.

This module defines the core data structures for K-line (candlestick) data,
including standardized formats, validation rules, and serialization methods.
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional
from decimal import Decimal

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from pydantic.types import PositiveInt


@dataclass
class KlineData:
    """
    Standardized K-line data structure matching Binance WebSocket format.
    
    This dataclass represents a single K-line (candlestick) with all the
    essential trading information including OHLCV data, timestamps, and
    trading statistics.
    """
    t: int              # Open time (timestamp in milliseconds)
    T: int              # Close time (timestamp in milliseconds)  
    s: str              # Symbol (e.g., "BTCUSDT")
    i: str              # Interval/timeframe (e.g., "1m", "1h", "1d")
    f: int              # First trade ID
    L: int              # Last trade ID
    o: str              # Open price (string to preserve precision)
    c: str              # Close price (string to preserve precision)
    h: str              # High price (string to preserve precision)
    l: str              # Low price (string to preserve precision)
    v: str              # Volume (base asset volume)
    n: int              # Number of trades
    x: bool             # Is kline closed (true if closed, false if open)
    q: str              # Quote asset volume
    V: str              # Taker buy base asset volume
    Q: str              # Taker buy quote asset volume
    B: str              # Ignore field (placeholder)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert KlineData to dictionary."""
        return {
            't': self.t,
            'T': self.T,
            's': self.s,
            'i': self.i,
            'f': self.f,
            'L': self.L,
            'o': self.o,
            'c': self.c,
            'h': self.h,
            'l': self.l,
            'v': self.v,
            'n': self.n,
            'x': self.x,
            'q': self.q,
            'V': self.V,
            'Q': self.Q,
            'B': self.B,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KlineData':
        """Create KlineData from dictionary."""
        return cls(
            t=data['t'],
            T=data['T'],
            s=data['s'],
            i=data['i'],
            f=data['f'],
            L=data['L'],
            o=data['o'],
            c=data['c'],
            h=data['h'],
            l=data['l'],
            v=data['v'],
            n=data['n'],
            x=data['x'],
            q=data['q'],
            V=data['V'],
            Q=data['Q'],
            B=data['B'],
        )
    
    def get_decimal_prices(self) -> Dict[str, Decimal]:
        """Get OHLC prices as Decimal objects for precise calculations."""
        return {
            'open': Decimal(self.o),
            'high': Decimal(self.h),
            'low': Decimal(self.l),
            'close': Decimal(self.c),
        }
    
    def get_decimal_volumes(self) -> Dict[str, Decimal]:
        """Get volume data as Decimal objects for precise calculations."""
        return {
            'volume': Decimal(self.v),
            'quote_volume': Decimal(self.q),
            'taker_buy_volume': Decimal(self.V),
            'taker_buy_quote_volume': Decimal(self.Q),
        }
    
    def is_valid_ohlc(self) -> bool:
        """
        Validate that OHLC prices follow the correct relationship:
        Low <= Open, Close <= High and Low <= High
        """
        try:
            prices = self.get_decimal_prices()
            low = prices['low']
            high = prices['high']
            open_price = prices['open']
            close = prices['close']
            
            return (low <= high and 
                    low <= open_price <= high and 
                    low <= close <= high)
        except (ValueError, TypeError):
            return False


class ValidatedKlineData(BaseModel):
    """
    Pydantic model for validated K-line data with comprehensive validation rules.
    
    This model provides runtime validation for all K-line fields, ensuring
    data integrity and proper format compliance.
    """
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid'
    )
    
    # Timestamps
    t: PositiveInt = Field(
        description="Open time (timestamp in milliseconds)",
        examples=[1640995200000, 1640995260000]
    )
    T: PositiveInt = Field(
        description="Close time (timestamp in milliseconds)",
        examples=[1640995259999, 1640995319999]
    )
    
    # Market identifiers
    s: str = Field(
        min_length=3,
        max_length=20,
        pattern=r'^[A-Z0-9]+$',
        description="Symbol (uppercase alphanumeric)",
        examples=["BTCUSDT", "ETHUSDT", "ADAUSDT"]
    )
    i: str = Field(
        pattern=r'^(1|3|5|15|30)m$|^(1|2|4|6|8|12)h$|^(1|3)d$|^1w$|^1M$',
        description="Interval/timeframe",
        examples=["1m", "5m", "1h", "1d"]
    )
    
    # Trade IDs
    f: PositiveInt = Field(
        description="First trade ID",
        examples=[1234567890, 9876543210]
    )
    L: PositiveInt = Field(
        description="Last trade ID", 
        examples=[1234567899, 9876543219]
    )
    
    # OHLC prices (stored as strings to preserve precision)
    o: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Open price (decimal string)",
        examples=["45000.12", "3200.456789"]
    )
    c: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Close price (decimal string)",
        examples=["45123.45", "3198.123456"]
    )
    h: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="High price (decimal string)",
        examples=["45200.00", "3205.789012"]
    )
    l: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Low price (decimal string)",
        examples=["44950.50", "3195.012345"]
    )
    
    # Volume data
    v: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Base asset volume (decimal string)",
        examples=["123.456789", "0.987654321"]
    )
    q: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Quote asset volume (decimal string)",
        examples=["5567890.12", "3141.592653"]
    )
    V: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Taker buy base asset volume (decimal string)",
        examples=["62.123456", "0.543210987"]
    )
    Q: str = Field(
        pattern=r'^\d+\.?\d*$',
        description="Taker buy quote asset volume (decimal string)",
        examples=["2784567.89", "1570.796326"]
    )
    
    # Trade statistics
    n: PositiveInt = Field(
        description="Number of trades",
        examples=[1500, 2750, 500]
    )
    
    # Status and metadata
    x: bool = Field(
        description="Is kline closed (true if closed, false if open)",
        examples=[True, False]
    )
    B: str = Field(
        default="0",
        description="Ignore field (reserved for future use)"
    )
    
    @field_validator('T', mode='after')
    @classmethod
    def validate_close_time(cls, v: int, info) -> int:
        """Validate that close time is after open time."""
        if 't' in info.data and v <= info.data['t']:
            raise ValueError('Close time must be after open time')
        return v
    
    @field_validator('L', mode='after')  
    @classmethod
    def validate_trade_ids(cls, v: int, info) -> int:
        """Validate that last trade ID is >= first trade ID."""
        if 'f' in info.data and v < info.data['f']:
            raise ValueError('Last trade ID must be >= first trade ID')
        return v
    
    @model_validator(mode='after')
    def validate_ohlc_relationships(self) -> 'ValidatedKlineData':
        """Validate that OHLC prices follow correct relationships."""
        try:
            open_price = Decimal(self.o)
            high = Decimal(self.h)
            low = Decimal(self.l)
            close = Decimal(self.c)
            
            # Validate high is the highest price
            if not (high >= max(open_price, low, close)):
                raise ValueError('High price must be >= all other OHLC prices')
            
            # Validate low is the lowest price
            if not (low <= min(open_price, high, close)): 
                raise ValueError('Low price must be <= all other OHLC prices')
                
        except (ValueError, TypeError) as e:
            if "must be" not in str(e):
                raise ValueError(f'Invalid OHLC price format: {e}')
            raise e
        
        return self
    
    def to_kline_data(self) -> KlineData:
        """Convert validated model to KlineData dataclass."""
        return KlineData(
            t=self.t,
            T=self.T,
            s=self.s,
            i=self.i,
            f=self.f,
            L=self.L,
            o=self.o,
            c=self.c,
            h=self.h,
            l=self.l,
            v=self.v,
            n=self.n,
            x=self.x,
            q=self.q,
            V=self.V,
            Q=self.Q,
            B=self.B,
        )
    
    @classmethod
    def from_kline_data(cls, kline: KlineData) -> 'ValidatedKlineData':
        """Create validated model from KlineData dataclass."""
        return cls(
            t=kline.t,
            T=kline.T,
            s=kline.s,
            i=kline.i,
            f=kline.f,
            L=kline.L,
            o=kline.o,
            c=kline.c,
            h=kline.h,
            l=kline.l,
            v=kline.v,
            n=kline.n,
            x=kline.x,
            q=kline.q,
            V=kline.V,
            Q=kline.Q,
            B=kline.B,
        )
"""
Unit tests for Redis client.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from kline_processor.storage.redis_client import RedisClient, RedisConnectionError, RedisOperationError


@pytest.fixture
def redis_client():
    """Create a RedisClient instance for testing."""
    return RedisClient()


@pytest.fixture
def mock_redis():
    """Create a mock Redis connection."""
    mock = AsyncMock()
    mock.ping.return_value = True
    mock.set.return_value = True
    mock.setex.return_value = True
    mock.get.return_value = None
    mock.delete.return_value = 0
    mock.exists.return_value = 0
    mock.expire.return_value = True
    mock.ttl.return_value = -1
    mock.zadd.return_value = 0
    mock.zrange.return_value = []
    mock.zrangebyscore.return_value = []
    mock.zrem.return_value = 0
    mock.zcard.return_value = 0
    mock.zremrangebyrank.return_value = 0
    mock.zremrangebyscore.return_value = 0
    mock.publish.return_value = 0
    mock.keys.return_value = []
    mock.flushdb.return_value = True
    mock.info.return_value = {}
    mock.aclose = AsyncMock()
    return mock


@pytest.fixture
def mock_pool():
    """Create a mock ConnectionPool."""
    mock = AsyncMock()
    mock.disconnect = AsyncMock()
    mock.created_connections = 5
    mock._available_connections = []
    mock._in_use_connections = []
    mock.get_connection = AsyncMock()
    return mock


class TestRedisClient:
    """Test cases for RedisClient class."""
    
    @pytest.mark.asyncio
    async def test_initialization(self, redis_client):
        """Test RedisClient initialization."""
        assert redis_client._pool is None
        assert redis_client._redis is None
        assert redis_client._is_connected is False
        assert redis_client.max_connections == 20
        assert redis_client.max_retries == 5
    
    @pytest.mark.asyncio
    async def test_connect_success(self, redis_client, mock_redis, mock_pool):
        """Test successful Redis connection."""
        with patch('redis.asyncio.ConnectionPool.from_url', return_value=mock_pool):
            # Mock the Redis constructor to return our mock
            with patch('redis.asyncio.client.Redis.__new__', return_value=mock_redis):
                await redis_client.connect()
                
                assert redis_client._is_connected is True
                assert redis_client._pool is mock_pool
                assert redis_client._redis is mock_redis
                mock_redis.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connect_already_connected(self, redis_client):
        """Test connecting when already connected."""
        redis_client._is_connected = True
        
        await redis_client.connect()
        
        # Should not attempt new connection
        assert redis_client._is_connected is True
    
    @pytest.mark.asyncio
    async def test_connect_failure(self, redis_client):
        """Test connection failure after max retries."""
        redis_client.max_retries = 1  # Reduce retries for faster test
        redis_client.retry_delay = 0.1  # Reduce delay for faster test
        
        with patch('redis.asyncio.ConnectionPool.from_url', side_effect=ConnectionError("Connection failed")):
            
            with pytest.raises(RedisConnectionError):
                await redis_client.connect()
            
            assert redis_client._is_connected is False
    
    @pytest.mark.asyncio
    async def test_disconnect(self, redis_client, mock_redis, mock_pool):
        """Test Redis disconnection."""
        # Set up connected state
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        redis_client._pool = mock_pool
        
        # Mock health check task
        mock_task = AsyncMock()
        mock_task.done.return_value = False
        mock_task.cancel = MagicMock()
        redis_client._health_check_task = mock_task
        
        await redis_client.disconnect()
        
        assert redis_client._is_connected is False
        assert redis_client._redis is None
        assert redis_client._pool is None
        mock_redis.aclose.assert_called_once()
        mock_pool.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_ping_success(self, redis_client, mock_redis):
        """Test successful ping."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        result = await redis_client.ping()
        
        assert result is True
        mock_redis.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_ping_failure(self, redis_client, mock_redis):
        """Test ping failure."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.ping.side_effect = Exception("Ping failed")
        
        result = await redis_client.ping()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_set_string_value(self, redis_client, mock_redis):
        """Test setting string value."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        result = await redis_client.set("test_key", "test_value")
        
        assert result is True
        mock_redis.set.assert_called_once_with("test_key", "test_value")
    
    @pytest.mark.asyncio
    async def test_set_json_value(self, redis_client, mock_redis):
        """Test setting JSON serializable value."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        test_dict = {"key": "value", "number": 123}
        
        result = await redis_client.set("test_key", test_dict)
        
        assert result is True
        expected_json = json.dumps(test_dict, default=str)
        mock_redis.set.assert_called_once_with("test_key", expected_json)
    
    @pytest.mark.asyncio
    async def test_set_with_expiration(self, redis_client, mock_redis):
        """Test setting value with expiration."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        result = await redis_client.set("test_key", "test_value", expire=60)
        
        assert result is True
        mock_redis.setex.assert_called_once_with("test_key", 60, "test_value")
    
    @pytest.mark.asyncio
    async def test_get_existing_key(self, redis_client, mock_redis):
        """Test getting existing key."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.get.return_value = "test_value"
        
        result = await redis_client.get("test_key")
        
        assert result == "test_value"
        mock_redis.get.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_get_json_value(self, redis_client, mock_redis):
        """Test getting JSON value."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        test_dict = {"key": "value", "number": 123}
        mock_redis.get.return_value = json.dumps(test_dict)
        
        result = await redis_client.get("test_key")
        
        assert result == test_dict
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_key(self, redis_client, mock_redis):
        """Test getting non-existent key."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.get.return_value = None
        
        result = await redis_client.get("test_key", default="default_value")
        
        assert result == "default_value"
    
    @pytest.mark.asyncio
    async def test_delete_keys(self, redis_client, mock_redis):
        """Test deleting keys."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.delete.return_value = 2
        
        result = await redis_client.delete("key1", "key2")
        
        assert result == 2
        mock_redis.delete.assert_called_once_with("key1", "key2")
    
    @pytest.mark.asyncio
    async def test_exists_keys(self, redis_client, mock_redis):
        """Test checking key existence."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.exists.return_value = 1
        
        result = await redis_client.exists("test_key")
        
        assert result == 1
        mock_redis.exists.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_expire_key(self, redis_client, mock_redis):
        """Test setting key expiration."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        result = await redis_client.expire("test_key", 300)
        
        assert result is True
        mock_redis.expire.assert_called_once_with("test_key", 300)
    
    @pytest.mark.asyncio
    async def test_ttl_key(self, redis_client, mock_redis):
        """Test getting key TTL."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.ttl.return_value = 120
        
        result = await redis_client.ttl("test_key")
        
        assert result == 120
        mock_redis.ttl.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_zadd_sorted_set(self, redis_client, mock_redis):
        """Test adding to sorted set."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zadd.return_value = 2
        mapping = {"member1": 1.0, "member2": 2.0}
        
        result = await redis_client.zadd("test_zset", mapping)
        
        assert result == 2
        mock_redis.zadd.assert_called_once_with("test_zset", mapping)
    
    @pytest.mark.asyncio
    async def test_zrange_sorted_set(self, redis_client, mock_redis):
        """Test getting range from sorted set."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zrange.return_value = ["member1", "member2"]
        
        result = await redis_client.zrange("test_zset", 0, -1)
        
        assert result == ["member1", "member2"]
        mock_redis.zrange.assert_called_once_with("test_zset", 0, -1, 
                                                  withscores=False, score_cast_func=float)
    
    @pytest.mark.asyncio
    async def test_zrangebyscore_sorted_set(self, redis_client, mock_redis):
        """Test getting range by score from sorted set."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zrangebyscore.return_value = ["member1"]
        
        result = await redis_client.zrangebyscore("test_zset", 1.0, 2.0)
        
        assert result == ["member1"]
        mock_redis.zrangebyscore.assert_called_once_with(
            "test_zset", 1.0, 2.0, start=None, num=None,
            withscores=False, score_cast_func=float
        )
    
    @pytest.mark.asyncio
    async def test_zrem_sorted_set(self, redis_client, mock_redis):
        """Test removing from sorted set."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zrem.return_value = 1
        
        result = await redis_client.zrem("test_zset", "member1")
        
        assert result == 1
        mock_redis.zrem.assert_called_once_with("test_zset", "member1")
    
    @pytest.mark.asyncio
    async def test_zcard_sorted_set(self, redis_client, mock_redis):
        """Test getting sorted set cardinality."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zcard.return_value = 5
        
        result = await redis_client.zcard("test_zset")
        
        assert result == 5
        mock_redis.zcard.assert_called_once_with("test_zset")
    
    @pytest.mark.asyncio
    async def test_publish_message(self, redis_client, mock_redis):
        """Test publishing message."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.publish.return_value = 3
        
        result = await redis_client.publish("test_channel", "test_message")
        
        assert result == 3
        mock_redis.publish.assert_called_once_with("test_channel", "test_message")
    
    @pytest.mark.asyncio
    async def test_publish_json_message(self, redis_client, mock_redis):
        """Test publishing JSON message."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.publish.return_value = 1
        test_dict = {"type": "update", "data": "value"}
        
        result = await redis_client.publish("test_channel", test_dict)
        
        assert result == 1
        expected_json = json.dumps(test_dict, default=str)
        mock_redis.publish.assert_called_once_with("test_channel", expected_json)
    
    @pytest.mark.asyncio
    async def test_keys_pattern(self, redis_client, mock_redis):
        """Test getting keys by pattern."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.keys.return_value = ["key1", "key2", "key3"]
        
        result = await redis_client.keys("test_*")
        
        assert result == ["key1", "key2", "key3"]
        mock_redis.keys.assert_called_once_with("test_*")
    
    @pytest.mark.asyncio
    async def test_zremrangebyrank(self, redis_client, mock_redis):
        """Test removing elements from sorted set by rank."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zremrangebyrank.return_value = 5
        
        result = await redis_client.zremrangebyrank("test_zset", 0, 4)
        
        assert result == 5
        mock_redis.zremrangebyrank.assert_called_once_with("test_zset", 0, 4)
    
    @pytest.mark.asyncio
    async def test_zremrangebyscore(self, redis_client, mock_redis):
        """Test removing elements from sorted set by score."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.zremrangebyscore.return_value = 3
        
        result = await redis_client.zremrangebyscore("test_zset", 1.0, 10.0)
        
        assert result == 3
        mock_redis.zremrangebyscore.assert_called_once_with("test_zset", 1.0, 10.0)
    
    @pytest.mark.asyncio
    async def test_flushdb(self, redis_client, mock_redis):
        """Test flushing database."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        result = await redis_client.flushdb()
        
        assert result is True
        mock_redis.flushdb.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_info(self, redis_client, mock_redis):
        """Test getting server info."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_info = {"redis_version": "6.2.0", "connected_clients": "5"}
        mock_redis.info.return_value = mock_info
        
        result = await redis_client.info()
        
        assert result == mock_info
        mock_redis.info.assert_called_once_with(None)
    
    def test_get_connection_stats(self, redis_client, mock_pool):
        """Test getting connection statistics."""
        redis_client._is_connected = True
        redis_client._connection_attempts = 2
        redis_client._last_health_check = datetime(2023, 1, 1, 12, 0, 0)
        redis_client._pool = mock_pool
        
        stats = redis_client.get_connection_stats()
        
        assert stats['is_connected'] is True
        assert stats['connection_attempts'] == 2
        assert stats['last_health_check'] == "2023-01-01T12:00:00"
        assert stats['pool_created_connections'] == 5
    
    @pytest.mark.asyncio
    async def test_context_manager(self, redis_client, mock_redis, mock_pool):
        """Test using RedisClient as async context manager."""
        with patch('redis.asyncio.ConnectionPool.from_url', return_value=mock_pool):
            with patch('redis.asyncio.client.Redis.__new__', return_value=mock_redis):
                async with redis_client as client:
                    assert client._is_connected is True
                    assert client is redis_client
                
                # Should disconnect after exiting context
                mock_redis.aclose.assert_called_once()
                mock_pool.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_operation_retry_on_connection_error(self, redis_client, mock_redis, mock_pool):
        """Test operation retry on connection error."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        # Simulate connection error
        mock_redis.get.side_effect = ConnectionError("Connection lost")
        
        result = await redis_client.get("test_key", default="default")
        # Should return default value due to connection error
        assert result == "default"
    
    @pytest.mark.asyncio
    async def test_operation_error_handling(self, redis_client, mock_redis):
        """Test operation error handling."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        mock_redis.get.side_effect = Exception("Operation failed")
        
        result = await redis_client.get("test_key", default="default")
        
        assert result == "default"
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, redis_client, mock_redis, mock_pool):
        """Test health check failure handling."""
        redis_client._is_connected = True
        redis_client._redis = mock_redis
        
        # Health check should set _is_connected to False on failure
        mock_redis.ping.side_effect = Exception("Health check failed")
        
        # Mock the connect method to prevent actual reconnection during test
        with patch.object(redis_client, 'connect') as mock_connect:
            mock_connect.side_effect = RedisConnectionError("Failed to reconnect")
            
            await redis_client._health_check()
            
            assert redis_client._is_connected is False
"""
Unit tests for Reconnection Handler.
"""

import pytest
import asyncio
import time
from unittest.mock import patch

from kline_processor.websocket.reconnection_handler import (
    ReconnectionHandler, ReconnectionStrategy, ReconnectionConfig, MaxReconnectionAttemptsExceeded
)


@pytest.fixture
def default_handler():
    """Create ReconnectionHandler with default configuration."""
    return ReconnectionHandler()


@pytest.fixture
def fixed_handler():
    """Create ReconnectionHandler with fixed strategy."""
    config = ReconnectionConfig(initial_delay=2.0, max_delay=10.0)
    return ReconnectionHandler(strategy=ReconnectionStrategy.FIXED, config=config)


@pytest.fixture
def linear_handler():
    """Create ReconnectionHandler with linear strategy."""
    config = ReconnectionConfig(initial_delay=1.0, max_delay=20.0)
    return ReconnectionHandler(strategy=ReconnectionStrategy.LINEAR, config=config)


@pytest.fixture
def jittered_handler():
    """Create ReconnectionHandler with jittered strategy."""
    config = ReconnectionConfig(
        initial_delay=1.0, 
        max_delay=30.0, 
        backoff_factor=2.0, 
        jitter_range=0.2
    )
    return ReconnectionHandler(strategy=ReconnectionStrategy.JITTERED, config=config)


@pytest.fixture
def limited_handler():
    """Create ReconnectionHandler with attempt limit."""
    config = ReconnectionConfig(initial_delay=1.0, max_attempts=3)
    return ReconnectionHandler(config=config)


class TestReconnectionConfig:
    """Test cases for ReconnectionConfig."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = ReconnectionConfig()
        
        assert config.initial_delay == 1.0
        assert config.max_delay == 300.0
        assert config.max_attempts is None
        assert config.backoff_factor == 2.0
        assert config.jitter_range == 0.1
        assert config.reset_threshold == 300.0
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = ReconnectionConfig(
            initial_delay=2.0,
            max_delay=60.0,
            max_attempts=5,
            backoff_factor=1.5,
            jitter_range=0.2,
            reset_threshold=120.0
        )
        
        assert config.initial_delay == 2.0
        assert config.max_delay == 60.0
        assert config.max_attempts == 5
        assert config.backoff_factor == 1.5
        assert config.jitter_range == 0.2
        assert config.reset_threshold == 120.0


class TestReconnectionHandler:
    """Test cases for ReconnectionHandler."""
    
    def test_initialization_default(self, default_handler):
        """Test default initialization."""
        assert default_handler.strategy == ReconnectionStrategy.EXPONENTIAL
        assert default_handler.config.initial_delay == 1.0
        assert default_handler.attempt_count == 0
        assert default_handler.total_attempts == 0
        assert default_handler.successful_reconnections == 0
        assert default_handler.failed_reconnections == 0
    
    def test_initialization_custom(self, fixed_handler):
        """Test custom initialization."""
        assert fixed_handler.strategy == ReconnectionStrategy.FIXED
        assert fixed_handler.config.initial_delay == 2.0
        assert fixed_handler.config.max_delay == 10.0
    
    @pytest.mark.asyncio
    async def test_get_next_delay_fixed(self, fixed_handler):
        """Test fixed delay strategy."""
        delay1 = await fixed_handler.get_next_delay()
        assert delay1 == 2.0
        assert fixed_handler.attempt_count == 1
        
        delay2 = await fixed_handler.get_next_delay()
        assert delay2 == 2.0
        assert fixed_handler.attempt_count == 2
    
    @pytest.mark.asyncio
    async def test_get_next_delay_linear(self, linear_handler):
        """Test linear delay strategy."""
        delay1 = await linear_handler.get_next_delay()
        assert delay1 == 1.0  # 1.0 * 1
        assert linear_handler.attempt_count == 1
        
        delay2 = await linear_handler.get_next_delay()
        assert delay2 == 2.0  # 1.0 * 2
        assert linear_handler.attempt_count == 2
        
        delay3 = await linear_handler.get_next_delay()
        assert delay3 == 3.0  # 1.0 * 3
        assert linear_handler.attempt_count == 3
    
    @pytest.mark.asyncio
    async def test_get_next_delay_exponential(self, default_handler):
        """Test exponential delay strategy."""
        delay1 = await default_handler.get_next_delay()
        assert delay1 == 1.0  # 1.0 * (2.0 ** 0)
        assert default_handler.attempt_count == 1
        
        delay2 = await default_handler.get_next_delay()
        assert delay2 == 2.0  # 1.0 * (2.0 ** 1)
        assert default_handler.attempt_count == 2
        
        delay3 = await default_handler.get_next_delay()
        assert delay3 == 4.0  # 1.0 * (2.0 ** 2)
        assert default_handler.attempt_count == 3
    
    @pytest.mark.asyncio
    async def test_get_next_delay_jittered(self, jittered_handler):
        """Test jittered delay strategy."""
        with patch('random.random', return_value=0.5):  # Fixed random value
            delay1 = await jittered_handler.get_next_delay()
            # Base delay: 1.0, jitter: 1.0 * 0.2 * (0.5 * 2 - 1) = 0.0
            assert delay1 == 1.0
        
        with patch('random.random', return_value=0.5):
            delay2 = await jittered_handler.get_next_delay()
            # Base delay: 2.0, jitter: 2.0 * 0.2 * 0.0 = 0.0
            assert delay2 == 2.0
    
    @pytest.mark.asyncio
    async def test_get_next_delay_max_limit(self, linear_handler):
        """Test delay respects maximum limit."""
        # Set attempt count high to exceed max delay
        linear_handler.attempt_count = 25
        
        delay = await linear_handler.get_next_delay()
        assert delay == linear_handler.config.max_delay  # Should be capped at 20.0
    
    @pytest.mark.asyncio
    async def test_get_next_delay_min_limit(self):
        """Test delay respects minimum limit."""
        config = ReconnectionConfig(initial_delay=0.05)  # Very small delay
        handler = ReconnectionHandler(config=config)
        
        delay = await handler.get_next_delay()
        assert delay >= 0.1  # Should be at least 0.1 seconds
    
    @pytest.mark.asyncio
    async def test_get_next_delay_max_attempts_exceeded(self, limited_handler):
        """Test max attempts exceeded exception."""
        # Exhaust all attempts
        await limited_handler.get_next_delay()  # Attempt 1
        await limited_handler.get_next_delay()  # Attempt 2
        await limited_handler.get_next_delay()  # Attempt 3
        
        # Next attempt should raise exception
        with pytest.raises(MaxReconnectionAttemptsExceeded):
            await limited_handler.get_next_delay()
    
    @pytest.mark.asyncio
    async def test_get_next_delay_statistics_tracking(self, default_handler):
        """Test statistics are properly tracked."""
        initial_time = default_handler.total_backoff_time
        
        delay = await default_handler.get_next_delay()
        
        assert default_handler.total_attempts == 1
        assert default_handler.attempt_count == 1
        assert default_handler.total_backoff_time == initial_time + delay
        assert default_handler.last_attempt_time is not None
    
    def test_on_connection_success(self, default_handler):
        """Test successful connection handling."""
        # Set initial state
        default_handler.attempt_count = 3
        default_handler.successful_reconnections = 1
        
        default_handler.on_connection_success()
        
        assert default_handler.successful_reconnections == 2
        assert default_handler.last_connection_time is not None
    
    def test_on_connection_success_with_reset(self, default_handler):
        """Test successful connection with counter reset."""
        # Set up for reset condition
        current_time = time.time()
        default_handler.last_attempt_time = current_time - 400  # 400 seconds ago
        default_handler.attempt_count = 5
        
        with patch('time.time', return_value=current_time):
            default_handler.on_connection_success()
        
        assert default_handler.attempt_count == 0  # Should be reset
        assert default_handler.last_attempt_time is None
    
    def test_on_connection_failure(self, default_handler):
        """Test connection failure handling."""
        error = Exception("Connection failed")
        default_handler.failed_reconnections = 2
        
        default_handler.on_connection_failure(error)
        
        assert default_handler.failed_reconnections == 3
    
    def test_reset(self, default_handler):
        """Test handler reset."""
        # Set some state
        default_handler.attempt_count = 5
        default_handler.last_attempt_time = time.time()
        
        default_handler.reset()
        
        assert default_handler.attempt_count == 0
        assert default_handler.last_attempt_time is None
    
    def test_should_attempt_reconnection_unlimited(self, default_handler):
        """Test reconnection check with unlimited attempts."""
        default_handler.attempt_count = 100
        
        assert default_handler.should_attempt_reconnection() is True
    
    def test_should_attempt_reconnection_limited(self, limited_handler):
        """Test reconnection check with limited attempts."""
        limited_handler.attempt_count = 2
        assert limited_handler.should_attempt_reconnection() is True
        
        limited_handler.attempt_count = 3
        assert limited_handler.should_attempt_reconnection() is False
    
    def test_get_statistics(self, default_handler):
        """Test getting handler statistics."""
        # Set up test data
        default_handler.attempt_count = 3
        default_handler.total_attempts = 10
        default_handler.successful_reconnections = 5
        default_handler.failed_reconnections = 4
        default_handler.total_backoff_time = 123.45
        default_handler.last_connection_time = 1640995200.0
        default_handler.last_attempt_time = 1640995300.0
        
        stats = default_handler.get_statistics()
        
        assert stats['strategy'] == 'exponential'
        assert stats['attempt_count'] == 3
        assert stats['total_attempts'] == 10
        assert stats['successful_reconnections'] == 5
        assert stats['failed_reconnections'] == 4
        assert stats['total_backoff_time_seconds'] == 123.45
        assert stats['last_connection_time'] == 1640995200.0
        assert stats['last_attempt_time'] == 1640995300.0
        
        # Check config section
        config_stats = stats['config']
        assert config_stats['initial_delay'] == 1.0
        assert config_stats['max_delay'] == 300.0
        assert config_stats['max_attempts'] is None
        assert config_stats['backoff_factor'] == 2.0
        assert config_stats['jitter_range'] == 0.1
        assert config_stats['reset_threshold'] == 300.0
    
    def test_get_statistics_with_custom_config(self, fixed_handler):
        """Test getting statistics with custom configuration."""
        stats = fixed_handler.get_statistics()
        
        assert stats['strategy'] == 'fixed'
        config_stats = stats['config']
        assert config_stats['initial_delay'] == 2.0
        assert config_stats['max_delay'] == 10.0


class TestMaxReconnectionAttemptsExceeded:
    """Test cases for MaxReconnectionAttemptsExceeded exception."""
    
    def test_exception_creation(self):
        """Test exception creation and message."""
        message = "Maximum attempts exceeded"
        exc = MaxReconnectionAttemptsExceeded(message)
        
        assert str(exc) == message
        assert isinstance(exc, Exception)
    
    def test_exception_inheritance(self):
        """Test exception inheritance."""
        exc = MaxReconnectionAttemptsExceeded("test")
        
        assert isinstance(exc, Exception)
        assert isinstance(exc, MaxReconnectionAttemptsExceeded)


class TestReconnectionStrategy:
    """Test cases for ReconnectionStrategy enum."""
    
    def test_strategy_values(self):
        """Test strategy enum values."""
        assert ReconnectionStrategy.FIXED.value == "fixed"
        assert ReconnectionStrategy.LINEAR.value == "linear"
        assert ReconnectionStrategy.EXPONENTIAL.value == "exponential"
        assert ReconnectionStrategy.JITTERED.value == "jittered"
    
    def test_strategy_comparison(self):
        """Test strategy enum comparison."""
        assert ReconnectionStrategy.FIXED == ReconnectionStrategy.FIXED
        assert ReconnectionStrategy.FIXED != ReconnectionStrategy.LINEAR
    
    def test_strategy_iteration(self):
        """Test iterating over strategies."""
        strategies = list(ReconnectionStrategy)
        
        assert len(strategies) == 4
        assert ReconnectionStrategy.FIXED in strategies
        assert ReconnectionStrategy.LINEAR in strategies
        assert ReconnectionStrategy.EXPONENTIAL in strategies
        assert ReconnectionStrategy.JITTERED in strategies
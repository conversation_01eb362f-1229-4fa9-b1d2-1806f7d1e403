"""
Error response models for consistent API error handling.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict


class ErrorDetail(BaseModel):
    """Detailed error information."""
    
    field: Optional[str] = Field(None, description="Field that caused the error")
    message: str = Field(..., description="Error message")
    code: Optional[str] = Field(None, description="Error code")


class ErrorResponse(BaseModel):
    """Standard error response format."""
    
    success: bool = Field(False, description="Always false for errors")
    error: str = Field(..., description="Error message")
    details: Optional[List[ErrorDetail]] = Field(None, description="Additional error details")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "success": False,
                "error": "Invalid request parameters",
                "details": [
                    {
                        "field": "symbol",
                        "message": "Symbol is required"
                    }
                ]
            }
        }
    }


class ValidationErrorResponse(BaseModel):
    """Validation error response with field-specific errors."""
    
    success: bool = Field(False, description="Always false for errors")
    error: str = Field("Validation error", description="General error message")
    validation_errors: Dict[str, List[str]] = Field(
        ...,
        description="Field-specific validation errors"
    )
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "success": False,
                "error": "Validation error",
                "validation_errors": {
                    "interval": ["Invalid interval. Must be one of: 1m, 3m, 5m, 15m, 30m, 1h, 4h, 1d"],
                    "limit": ["Limit must be between 1 and 1000"]
                }
            }
        }
    }
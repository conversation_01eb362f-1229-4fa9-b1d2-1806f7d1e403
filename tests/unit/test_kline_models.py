"""
Unit tests for K-line data models.
"""

import pytest
from decimal import Decimal
from pydantic import ValidationError

from kline_processor.models.kline import KlineData, ValidatedKlineData


class TestKlineData:
    """Test cases for KlineData dataclass."""
    
    def test_kline_data_creation(self):
        """Test basic KlineData creation."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        assert kline.t == 1640995200000
        assert kline.s == "BTCUSDT"
        assert kline.i == "1m"
        assert kline.x is True
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        data_dict = kline.to_dict()
        assert data_dict['t'] == 1640995200000
        assert data_dict['s'] == "BTCUSDT"
        assert data_dict['x'] is True
        assert len(data_dict) == 17  # All fields
    
    def test_from_dict(self):
        """Test creation from dictionary."""
        data = {
            't': 1640995200000,
            'T': 1640995259999,
            's': "BTCUSDT",
            'i': "1m",
            'f': 1234567890,
            'L': 1234567899,
            'o': "45000.12",
            'c': "45123.45",
            'h': "45200.00",
            'l': "44950.50",
            'v': "123.456789",
            'n': 1500,
            'x': True,
            'q': "5567890.12",
            'V': "62.123456",
            'Q': "2784567.89",
            'B': "0"
        }
        
        kline = KlineData.from_dict(data)
        assert kline.t == 1640995200000
        assert kline.s == "BTCUSDT"
        assert kline.x is True
    
    def test_get_decimal_prices(self):
        """Test getting prices as Decimal objects."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        prices = kline.get_decimal_prices()
        assert prices['open'] == Decimal("45000.12")
        assert prices['high'] == Decimal("45200.00")
        assert prices['low'] == Decimal("44950.50")
        assert prices['close'] == Decimal("45123.45")
    
    def test_get_decimal_volumes(self):
        """Test getting volumes as Decimal objects."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        volumes = kline.get_decimal_volumes()
        assert volumes['volume'] == Decimal("123.456789")
        assert volumes['quote_volume'] == Decimal("5567890.12")
        assert volumes['taker_buy_volume'] == Decimal("62.123456")
        assert volumes['taker_buy_quote_volume'] == Decimal("2784567.89")
    
    def test_is_valid_ohlc_true(self):
        """Test OHLC validation with valid data."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        assert kline.is_valid_ohlc() is True
    
    def test_is_valid_ohlc_false(self):
        """Test OHLC validation with invalid data."""
        kline = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="44000.00",  # High price lower than open - invalid
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        assert kline.is_valid_ohlc() is False


class TestValidatedKlineData:
    """Test cases for ValidatedKlineData Pydantic model."""
    
    def test_valid_kline_data_creation(self):
        """Test creation of valid KlineData."""
        kline = ValidatedKlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89"
        )
        
        assert kline.t == 1640995200000
        assert kline.s == "BTCUSDT"
        assert kline.i == "1m"
        assert kline.x is True
    
    def test_invalid_symbol(self):
        """Test validation error for invalid symbol."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995200000,
                T=1640995259999,
                s="btcusdt",  # lowercase not allowed
                i="1m",
                f=1234567890,
                L=1234567899,
                o="45000.12",
                c="45123.45",
                h="45200.00",
                l="44950.50",
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "string_pattern_mismatch" in str(exc_info.value)
    
    def test_invalid_interval(self):
        """Test validation error for invalid interval."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995200000,
                T=1640995259999,
                s="BTCUSDT",
                i="2m",  # not in allowed pattern
                f=1234567890,
                L=1234567899,
                o="45000.12",
                c="45123.45",
                h="45200.00",
                l="44950.50",
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "string_pattern_mismatch" in str(exc_info.value)
    
    def test_invalid_price_format(self):
        """Test validation error for invalid price format."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995200000,
                T=1640995259999,
                s="BTCUSDT",
                i="1m",
                f=1234567890,
                L=1234567899,
                o="45000.12.34",  # invalid decimal format
                c="45123.45",
                h="45200.00",
                l="44950.50",
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "string_pattern_mismatch" in str(exc_info.value)
    
    def test_close_time_validation(self):
        """Test validation that close time is after open time."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995259999,
                T=1640995200000,  # close time before open time
                s="BTCUSDT",
                i="1m",
                f=1234567890,
                L=1234567899,
                o="45000.12",
                c="45123.45",
                h="45200.00",
                l="44950.50",
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "Close time must be after open time" in str(exc_info.value)
    
    def test_trade_id_validation(self):
        """Test validation that last trade ID >= first trade ID."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995200000,
                T=1640995259999,
                s="BTCUSDT",
                i="1m",
                f=1234567899,
                L=1234567890,  # last ID less than first ID
                o="45000.12",
                c="45123.45",
                h="45200.00",
                l="44950.50",
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "Last trade ID must be >= first trade ID" in str(exc_info.value)
    
    def test_ohlc_price_validation_high_too_low(self):
        """Test validation that high price is highest."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995200000,
                T=1640995259999,
                s="BTCUSDT",
                i="1m",
                f=1234567890,
                L=1234567899,
                o="45000.12",
                c="45123.45",  
                h="45100.00",  # high lower than close (45123.45)
                l="44950.50",
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "High price must be >= all other OHLC prices" in str(exc_info.value)
    
    def test_ohlc_price_validation_low_too_high(self):
        """Test validation that low price is lowest."""
        with pytest.raises(ValidationError) as exc_info:
            ValidatedKlineData(
                t=1640995200000,
                T=1640995259999,
                s="BTCUSDT",
                i="1m",
                f=1234567890,
                L=1234567899,
                o="45000.12",
                c="45000.00",  # close slightly lower
                h="45100.00",  # high higher than low to pass high validation
                l="45010.00",  # low higher than close - should fail low validation
                v="123.456789",
                n=1500,
                x=True,
                q="5567890.12",
                V="62.123456",
                Q="2784567.89"
            )
        
        assert "Low price must be <= all other OHLC prices" in str(exc_info.value)
    
    def test_to_kline_data(self):
        """Test conversion to KlineData dataclass."""
        validated_kline = ValidatedKlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89"
        )
        
        kline_data = validated_kline.to_kline_data()
        assert isinstance(kline_data, KlineData)
        assert kline_data.t == 1640995200000
        assert kline_data.s == "BTCUSDT"
        assert kline_data.x is True
    
    def test_from_kline_data(self):
        """Test creation from KlineData dataclass."""
        kline_data = KlineData(
            t=1640995200000,
            T=1640995259999,
            s="BTCUSDT",
            i="1m",
            f=1234567890,
            L=1234567899,
            o="45000.12",
            c="45123.45",
            h="45200.00",
            l="44950.50",
            v="123.456789",
            n=1500,
            x=True,
            q="5567890.12",
            V="62.123456",
            Q="2784567.89",
            B="0"
        )
        
        validated_kline = ValidatedKlineData.from_kline_data(kline_data)
        assert isinstance(validated_kline, ValidatedKlineData)
        assert validated_kline.t == 1640995200000
        assert validated_kline.s == "BTCUSDT"
        assert validated_kline.x is True